# ApplicationCenter Backend

APP版本管理系统后端服务

## 技术栈

- **开发语言**: Java 17
- **框架**: Spring Boot 3.1.5
- **数据库**: MySQL 8.0+
- **ORM**: Spring Data JPA
- **安全**: Spring Security + JWT
- **构建工具**: Maven

## 项目结构

```
src/main/java/com/applicationcenter/backend/
├── ApplicationCenterBackendApplication.java  # 启动类
├── controller/                               # 控制器层
├── service/                                 # 业务逻辑层
├── repository/                              # 数据访问层
├── model/                                   # 实体类
├── config/                                  # 配置类
└── util/                                    # 工具类
```

## 环境要求

- JDK 17+
- MySQL 8.0+
- Maven 3.6+

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd ApplicationCenter-backend
```

### 2. 配置数据库
- 创建数据库：`application_center`
- 修改 `src/main/resources/application.yml` 中的数据库连接信息

### 3. 运行项目
```bash
# 使用Maven运行
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/application-center-backend-0.0.1-SNAPSHOT.jar
```

### 4. 访问接口
- 服务地址：http://localhost:8080/api
- 健康检查：http://localhost:8080/api/actuator/health

## 主要功能

- 管理员认证与授权
- 应用管理（增删改查）
- 版本管理（上传、发布、下架）
- 文件上传与下载
- 操作日志记录
- 数据统计

## API文档

启动项目后，可通过以下地址查看API文档：
- Swagger UI：http://localhost:8080/api/swagger-ui.html
- API文档：http://localhost:8080/api/v3/api-docs

## 开发说明

### 数据库初始化
项目启动时会自动创建表结构，也可以手动执行 `sql/init.sql` 文件。

### 配置文件
主要配置文件：`src/main/resources/application.yml`
- 数据库连接配置
- JWT配置
- 文件上传配置
- 日志配置

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的API响应格式
- 所有接口都需要进行参数校验
- 管理接口需要JWT认证

## 部署

### 开发环境
```bash
mvn spring-boot:run
```

### 生产环境
```bash
# 打包
mvn clean package -Dmaven.test.skip=true

# 运行
java -jar target/application-center-backend-0.0.1-SNAPSHOT.jar
```

## 联系方式

如有问题，请联系开发团队。 