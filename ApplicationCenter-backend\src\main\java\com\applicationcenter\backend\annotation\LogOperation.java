package com.applicationcenter.backend.annotation;

import java.lang.annotation.*;

/**
 * 日志操作注解
 * 用于标记需要自动记录操作日志的方法
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogOperation {
    
    /**
     * 操作描述
     */
    String description() default "";
    
    /**
     * 模块名称
     */
    String module() default "";
    
    /**
     * 日志级别
     */
    LogLevel level() default LogLevel.INFO;
    
    /**
     * 是否记录请求参数
     */
    boolean logParams() default true;
    
    /**
     * 是否记录响应结果
     */
    boolean logResponse() default false;
    
    /**
     * 是否记录执行时间
     */
    boolean logExecutionTime() default true;
    
    /**
     * 日志级别枚举
     */
    enum LogLevel {
        INFO, WARN, ERROR, DEBUG
    }
} 