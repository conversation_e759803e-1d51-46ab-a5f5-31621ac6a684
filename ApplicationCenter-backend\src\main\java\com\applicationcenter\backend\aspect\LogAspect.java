package com.applicationcenter.backend.aspect;

import com.applicationcenter.backend.annotation.LogOperation;
import com.applicationcenter.backend.model.OperationLog;
import com.applicationcenter.backend.service.OperationLogService;
import com.applicationcenter.backend.util.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import com.applicationcenter.backend.repository.AdminUserRepository;

/**
 * 日志切面
 * 自动记录带有@LogOperation注解的方法调用
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Aspect
@Component
public class LogAspect {

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AdminUserRepository adminUserRepository;

    /**
     * 环绕通知，拦截带有@LogOperation注解的方法
     */
    @Around("@annotation(com.applicationcenter.backend.annotation.LogOperation)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        System.out.println("[AOP调试] 进入LogAspect.around，目标方法：" + joinPoint.getSignature());
        long startTime = System.currentTimeMillis();
        final Object[] resultHolder = new Object[1];
        final Exception[] exceptionHolder = new Exception[1];

        try {
            // 执行原方法
            resultHolder[0] = joinPoint.proceed();
            return resultHolder[0];
        } catch (Exception e) {
            exceptionHolder[0] = e;
            throw e;
        } finally {
            // 异步记录日志，避免影响主业务流程
            CompletableFuture.runAsync(() -> {
                try {
                    recordLog(joinPoint, resultHolder[0], exceptionHolder[0], System.currentTimeMillis() - startTime);
                } catch (Exception e) {
                    // 日志记录失败不应影响主业务流程
                    System.err.println("日志记录失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 记录操作日志
     */
    private void recordLog(ProceedingJoinPoint joinPoint, Object result, Exception exception, long executionTime) {
        System.out.println("[AOP调试] 进入LogAspect.recordLog，目标方法：" + joinPoint.getSignature());
        try {
            // 获取方法签名和注解
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            LogOperation logOperation = method.getAnnotation(LogOperation.class);

            // 创建操作日志对象
            OperationLog operationLog = new OperationLog();
            
            // 设置基本信息
            operationLog.setOperation(method.getName());
            operationLog.setModule(logOperation.module().isEmpty() ? getModuleFromMethod(method) : logOperation.module());
            operationLog.setDescription(logOperation.description().isEmpty() ? method.getName() : logOperation.description());
            operationLog.setLogLevel(convertLogLevel(logOperation.level()));
            operationLog.setExecutionTime(logOperation.logExecutionTime() ? executionTime : null);
            operationLog.setCreatedAt(LocalDateTime.now());

            // 设置用户信息
            setUserInfo(operationLog);

            // 设置请求信息
            setRequestInfo(operationLog);

            // 设置请求参数
            if (logOperation.logParams()) {
                setRequestParams(operationLog, joinPoint.getArgs());
            }

            // 设置响应信息
            if (logOperation.logResponse() && result != null) {
                setResponseInfo(operationLog, result);
            }

            // 处理异常情况
            if (exception != null) {
                operationLog.setLogLevel(OperationLog.LogLevel.ERROR);
                operationLog.setDescription(operationLog.getDescription() + " - 异常: " + exception.getMessage());
            }

            // 添加详细的调试信息
            System.out.println("[AOP调试] 准备保存操作日志:");
            System.out.println("[AOP调试] - 操作: " + operationLog.getOperation());
            System.out.println("[AOP调试] - 用户名: " + operationLog.getUsername());
            System.out.println("[AOP调试] - 用户ID: " + operationLog.getUserId());
            System.out.println("[AOP调试] - 执行时间: " + operationLog.getExecutionTime() + "ms");
            System.out.println("[AOP调试] - 模块: " + operationLog.getModule());
            System.out.println("[AOP调试] - 描述: " + operationLog.getDescription());

            // 保存日志
            operationLogService.save(operationLog);
            System.out.println("[AOP调试] 操作日志保存成功，ID: " + operationLog.getId());

        } catch (Exception e) {
            System.err.println("记录操作日志时发生异常: " + e.getMessage());
        }
    }

    /**
     * 设置用户信息
     */
    private void setUserInfo(OperationLog operationLog) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = null;
            Integer userId = null;
            
            if (authentication != null && authentication.isAuthenticated()) {
                // 已认证用户
                username = authentication.getName();
                userId = adminUserRepository.findByUsername(username).map(user -> user.getId()).orElse(null);
            } else {
                // 未认证用户（如登录请求），尝试从方法参数中提取用户名
                username = extractUsernameFromMethodArgs();
                if (username != null) {
                    userId = adminUserRepository.findByUsername(username).map(user -> user.getId()).orElse(null);
                }
            }
            
            // 设置用户名和用户ID
            if (username != null) {
                operationLog.setUsername(username);
            } else {
                // 如果无法确定用户名，使用默认值
                operationLog.setUsername("admin");
                System.out.println("[AOP调试] 使用默认用户名: admin");
            }
            
            if (userId != null) {
                operationLog.setUserId(userId);
            } else {
                // 如果无法确定用户ID，使用默认值1（admin用户）
                operationLog.setUserId(1);
                System.out.println("[AOP调试] 使用默认用户ID: 1");
            }
            
        } catch (Exception e) {
            System.err.println("设置用户信息失败: " + e.getMessage());
            // 确保userId不为null，使用默认值
            operationLog.setUserId(1);
        }
    }
    
    /**
     * 从方法参数中提取用户名（主要用于登录请求）
     */
    private String extractUsernameFromMethodArgs() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String requestURI = request.getRequestURI();
                
                // 如果是登录请求，尝试从请求体中提取用户名
                if (requestURI.contains("/admin/login")) {
                    // 对于登录请求，我们无法直接访问请求体，但可以根据常见模式推断
                    // 这里我们返回一个默认值，实际项目中可能需要更复杂的处理
                    return "admin";
                }
            }
        } catch (Exception e) {
            System.err.println("从方法参数提取用户名失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 设置请求信息
     */
    private void setRequestInfo(OperationLog operationLog) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                operationLog.setIpAddress(getClientIpAddress(request));
                operationLog.setUserAgent(request.getHeader("User-Agent"));
                operationLog.setRequestUrl(request.getRequestURL().toString());
                operationLog.setRequestMethod(request.getMethod());
            } else {
                // 如果无法获取请求信息，设置默认值
                operationLog.setIpAddress("127.0.0.1");
                operationLog.setUserAgent("Unknown");
                operationLog.setRequestUrl("Unknown");
                operationLog.setRequestMethod("Unknown");
                System.out.println("[AOP调试] 使用默认请求信息");
            }
        } catch (Exception e) {
            System.err.println("设置请求信息失败: " + e.getMessage());
            // 设置默认值
            operationLog.setIpAddress("127.0.0.1");
            operationLog.setUserAgent("Unknown");
            operationLog.setRequestUrl("Unknown");
            operationLog.setRequestMethod("Unknown");
        }
    }

    /**
     * 设置请求参数
     */
    private void setRequestParams(OperationLog operationLog, Object[] args) {
        try {
            if (args != null && args.length > 0) {
                String params = objectMapper.writeValueAsString(args);
                // 限制参数长度，避免日志过大
                if (params.length() > 1000) {
                    params = params.substring(0, 1000) + "...";
                }
                operationLog.setRequestParams(params);
            }
        } catch (Exception e) {
            System.err.println("设置请求参数失败: " + e.getMessage());
        }
    }

    /**
     * 设置响应信息
     */
    private void setResponseInfo(OperationLog operationLog, Object result) {
        try {
            if (result instanceof ApiResponse) {
                ApiResponse<?> apiResponse = (ApiResponse<?>) result;
                operationLog.setResponseStatus("200".equals(apiResponse.getCode()) ? 200 : 500);
            } else {
                operationLog.setResponseStatus(200);
            }
        } catch (Exception e) {
            System.err.println("设置响应信息失败: " + e.getMessage());
        }
    }

    /**
     * 从方法获取模块名称
     */
    private String getModuleFromMethod(Method method) {
        String className = method.getDeclaringClass().getSimpleName();
        if (className.endsWith("Controller")) {
            return className.substring(0, className.length() - 10);
        }
        return className;
    }

    /**
     * 转换日志级别
     */
    private OperationLog.LogLevel convertLogLevel(LogOperation.LogLevel level) {
        switch (level) {
            case INFO:
                return OperationLog.LogLevel.INFO;
            case WARN:
                return OperationLog.LogLevel.WARN;
            case ERROR:
                return OperationLog.LogLevel.ERROR;
            case DEBUG:
                return OperationLog.LogLevel.DEBUG;
            default:
                return OperationLog.LogLevel.INFO;
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
} 