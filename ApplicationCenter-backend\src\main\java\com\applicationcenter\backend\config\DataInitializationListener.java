package com.applicationcenter.backend.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

/**
 * 数据初始化监听器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Component
public class DataInitializationListener {
    
    private static final Logger logger = LoggerFactory.getLogger(DataInitializationListener.class);
    
    private final JdbcTemplate jdbcTemplate;
    private final PasswordEncoder passwordEncoder;
    
    public DataInitializationListener(JdbcTemplate jdbcTemplate, PasswordEncoder passwordEncoder) {
        this.jdbcTemplate = jdbcTemplate;
        this.passwordEncoder = passwordEncoder;
    }
    
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        try {
            // 检查并创建表结构
            checkAndCreateTables();
            
            // 检查并插入初始数据
            checkAndInsertData();
            
            // 最终检查所有表的数据状态
            checkAllTablesData();
            
        } catch (Exception e) {
            logger.error("数据库初始化检查失败: {}", e.getMessage());
        }
    }
    
    /**
     * 检查并创建表结构
     */
    private void checkAndCreateTables() {
        try {
            
            // 检查各个表是否存在
            boolean adminTableExists = checkTableExists("admin_users");
            boolean appTableExists = checkTableExists("applications");
            boolean versionTableExists = checkTableExists("versions");
            boolean logTableExists = checkTableExists("operation_logs");
            
            
            // 如果有表不存在，则执行schema.sql创建所有表
            if (!adminTableExists || !appTableExists || !versionTableExists || !logTableExists) {
                executeSchemaInitialization();
            } else {
            }
            
        } catch (Exception e) {
            logger.error("表结构检查失败: {}", e.getMessage());
        }
    }
    
    /**
     * 检查表是否存在
     */
    private boolean checkTableExists(String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            return count != null && count > 0;
        } catch (Exception e) {
            logger.warn("检查表 {} 是否存在时出错: {}", tableName, e.getMessage());
            return false;
        }
    }
    
    /**
     * 执行表结构初始化
     */
    private void executeSchemaInitialization() {
        try {
            
            // 按正确顺序创建表（先创建被引用的表）
            createTableIfNotExists("admin_users", getAdminUsersTableSQL());
            createTableIfNotExists("applications", getApplicationsTableSQL());
            createTableIfNotExists("versions", getVersionsTableSQL());
            createTableIfNotExists("operation_logs", getOperationLogsTableSQL());
            
        } catch (Exception e) {
            logger.error("表结构初始化失败: {}", e.getMessage());
        }
    }
    
    /**
     * 创建表（如果不存在）
     */
    private void createTableIfNotExists(String tableName, String createSQL) {
        try {
            if (!checkTableExists(tableName)) {
                jdbcTemplate.execute(createSQL);
            } else {
            }
        } catch (Exception e) {
            logger.error("❌ 创建表 {} 失败: {}", tableName, e.getMessage());
        }
    }
    
    /**
     * 获取管理员表创建SQL
     */
    private String getAdminUsersTableSQL() {
        return """
            CREATE TABLE admin_users (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
                username VARCHAR(50) NOT NULL UNIQUE COMMENT '管理员用户名',
                password VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
                status TINYINT DEFAULT 1 COMMENT '状态(1启用/0禁用)',
                last_login DATETIME COMMENT '最后登录时间',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表'
            """;
    }
    
    /**
     * 获取应用表创建SQL
     */
    private String getApplicationsTableSQL() {
        return """
            CREATE TABLE applications (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
                name VARCHAR(100) NOT NULL COMMENT '应用名称',
                package_name VARCHAR(100) NOT NULL COMMENT '包名/Bundle ID',
                platform ENUM('ios','android','harmony') NOT NULL DEFAULT 'android' COMMENT '平台类型',
                description TEXT COMMENT '应用描述',
                icon VARCHAR(255) COMMENT '应用图标URL',
                category VARCHAR(50) COMMENT '应用分类',
                developer VARCHAR(100) COMMENT '开发者',
                website VARCHAR(255) COMMENT '官网地址',
                status TINYINT DEFAULT 1 COMMENT '状态(1启用/0禁用)',
                created_by INT COMMENT '创建人ID（管理员）',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用表'
            """;
    }
    
    /**
     * 获取版本表创建SQL
     */
    private String getVersionsTableSQL() {
        return """
            CREATE TABLE versions (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
                application_id INT NOT NULL COMMENT '应用ID（外键）',
                version_code VARCHAR(20) NOT NULL COMMENT '版本号',
                version_name VARCHAR(50) COMMENT '版本名称',
                build_number INT COMMENT '构建号',
                file_size BIGINT COMMENT '文件大小（字节）',
                file_url VARCHAR(255) NOT NULL COMMENT '安装包URL',
                download_count INT DEFAULT 0 COMMENT '下载次数',
                changelog TEXT COMMENT '更新日志',
                min_os_version VARCHAR(20) COMMENT '最低系统版本',
                is_force_update TINYINT DEFAULT 0 COMMENT '是否强制更新',
                status ENUM('published','unpublished') DEFAULT 'published' COMMENT '状态（已发布/已下架）',
                published_at DATETIME COMMENT '发布时间',
                created_by INT COMMENT '创建人ID（管理员）',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                FOREIGN KEY (application_id) REFERENCES applications(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本表'
            """;
    }
    
    /**
     * 获取操作日志表创建SQL
     */
    private String getOperationLogsTableSQL() {
        return """
            CREATE TABLE operation_logs (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
                admin_id INT NOT NULL COMMENT '管理员ID（外键）',
                action VARCHAR(50) NOT NULL COMMENT '操作类型',
                target_type VARCHAR(50) NOT NULL COMMENT '目标类型(app/version)',
                target_id INT COMMENT '目标ID',
                description TEXT COMMENT '操作描述',
                ip_address VARCHAR(45) COMMENT 'IP地址',
                user_agent TEXT COMMENT '用户代理',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                FOREIGN KEY (admin_id) REFERENCES admin_users(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表'
            """;
    }
    
    /**
     * 检查并插入初始数据
     */
    private void checkAndInsertData() {
        try {
            
            // 检查各个表的数据条数
            Integer adminCount = getTableCount("admin_users");
            Integer appCount = getTableCount("applications");
            Integer versionCount = getTableCount("versions");
            
            
            // 如果任何表为空，则执行数据初始化
            if (adminCount == 0 || appCount == 0 || versionCount == 0) {
                executeDataInitialization();
            } else {
            }
            
        } catch (Exception e) {
            logger.error("数据检查失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取表的数据条数
     */
    private Integer getTableCount(String tableName) {
        try {
            return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM " + tableName, Integer.class);
        } catch (Exception e) {
            logger.warn("获取表 {} 数据条数失败: {}", tableName, e.getMessage());
            return 0;
        }
    }
    
    /**
     * 最终检查所有表的数据状态
     */
    private void checkAllTablesData() {
        try {
            
            // 检查各个表的数据条数
            Integer adminCount = getTableCount("admin_users");
            Integer appCount = getTableCount("applications");
            Integer versionCount = getTableCount("versions");
            Integer logCount = getTableCount("operation_logs");
            
            
        } catch (Exception e) {
            logger.error("最终数据检查失败: {}", e.getMessage());
        }
    }
    
    /**
     * 执行数据初始化
     */
    private void executeDataInitialization() {
        try {
            
            // 直接执行插入语句，避免ON DUPLICATE KEY UPDATE的问题
            insertAdminUsers();
            insertApplications();
            insertVersions();
            
        } catch (Exception e) {
            logger.error("数据初始化失败: {}", e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 插入管理员数据
     */
    private void insertAdminUsers() {
        try {
            // 检查是否已有管理员数据
            Integer count = getTableCount("admin_users");
            if (count > 0) {
                return;
            }
            
            
            // 插入默认管理员账号
            String adminPassword = passwordEncoder.encode("admin123");
            String adminSQL = """
                INSERT INTO admin_users (username, password, status, created_at, updated_at) 
                VALUES ('admin', ?, 1, NOW(), NOW())
                """;
            jdbcTemplate.update(adminSQL, adminPassword);
            
            
            // 插入测试管理员账号
            String testPassword = passwordEncoder.encode("test123");
            String testSQL = """
                INSERT INTO admin_users (username, password, status, created_at, updated_at) 
                VALUES ('test', ?, 1, NOW(), NOW())
                """;
            jdbcTemplate.update(testSQL, testPassword);
            
        } catch (Exception e) {
            logger.error("插入管理员数据失败: {}", e.getMessage());
        }
    }
    
    /**
     * 插入应用数据
     */
    private void insertApplications() {
        try {
            // 检查是否已有应用数据
            Integer count = getTableCount("applications");
            if (count > 0) {
                return;
            }
            
            
            String appSQL = """
                INSERT INTO applications (name, package_name, platform, description, category, developer, status, created_by, created_at, updated_at) 
                VALUES 
                ('示例应用1', 'com.example.app1', 'android', '这是一个示例Android应用', '工具', '示例开发者', 1, 1, NOW(), NOW()),
                ('示例应用2', 'com.example.app2', 'ios', '这是一个示例iOS应用', '娱乐', '示例开发者', 1, 1, NOW(), NOW())
                """;
            jdbcTemplate.execute(appSQL);
            
        } catch (Exception e) {
            logger.error("插入应用数据失败: {}", e.getMessage());
        }
    }
    
    /**
     * 插入版本数据
     */
    private void insertVersions() {
        try {
            // 检查是否已有版本数据
            Integer count = getTableCount("versions");
            if (count > 0) {
                return;
            }
            
            
            String versionSQL = """
                INSERT INTO versions (application_id, version_code, version_name, build_number, file_size, file_url, changelog, status, created_by, created_at, updated_at) 
                VALUES 
                (1, '1.0.0', '初始版本', 1, 1024000, '/uploads/apps/1/versions/1.0.0.apk', '初始版本发布', 'published', 1, NOW(), NOW()),
                (1, '1.1.0', '功能更新', 2, 1056000, '/uploads/apps/1/versions/1.1.0.apk', '修复已知问题，优化性能', 'published', 1, NOW(), NOW()),
                (2, '1.0.0', '初始版本', 1, 2048000, '/uploads/apps/2/versions/1.0.0.ipa', '初始版本发布', 'published', 1, NOW(), NOW())
                """;
            jdbcTemplate.execute(versionSQL);
            
        } catch (Exception e) {
            logger.error("插入版本数据失败: {}", e.getMessage());
        }
    }
} 