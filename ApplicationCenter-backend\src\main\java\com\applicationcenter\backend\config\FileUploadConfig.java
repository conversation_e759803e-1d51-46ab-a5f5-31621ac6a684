package com.applicationcenter.backend.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.InitializingBean;

import java.io.File;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Configuration
public class FileUploadConfig implements InitializingBean {
    @Value("${file.upload.path}")
    private String configuredPath;

    private String resolvedUploadPath;

    @Override
    public void afterPropertiesSet() {
        if (configuredPath != null && configuredPath.startsWith("jar_dir")) {
            String jarDir = getJarDir();
            resolvedUploadPath = jarDir + File.separator + configuredPath.replaceFirst("jar_dir[\\/]*", "uploads");
        } else {
            resolvedUploadPath = configuredPath;
        }
        // 自动创建目录
        try {
            Path uploadDir = Paths.get(resolvedUploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }
        } catch (Exception e) {
            throw new RuntimeException("无法创建上传目录: " + resolvedUploadPath, e);
        }
    }

    private String getJarDir() {
        try {
            String path = FileUploadConfig.class.getProtectionDomain().getCodeSource().getLocation().toURI().getPath();
            File jarFile = new File(path);
            File dir = jarFile.isFile() ? jarFile.getParentFile() : jarFile;
            return dir.getAbsolutePath();
        } catch (Exception e) {
            throw new RuntimeException("无法获取jar包目录", e);
        }
    }

    @Bean(name = "uploadPath")
    public String uploadPath() {
        return resolvedUploadPath;
    }
} 