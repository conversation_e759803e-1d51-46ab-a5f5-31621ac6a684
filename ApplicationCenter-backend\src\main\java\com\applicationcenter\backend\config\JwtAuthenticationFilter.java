package com.applicationcenter.backend.config;

import com.applicationcenter.backend.util.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.util.AntPathMatcher;

import java.io.IOException;

/**
 * JWT认证过滤器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private UserDetailsService userDetailsService;
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        // 增强调试日志
            System.out.println("=== JWT过滤器调试信息 ===");
        System.out.println("请求方法: " + method);
            System.out.println("请求URI: " + requestURI);
            System.out.println("Content-Type: " + request.getContentType());
            System.out.println("Authorization头: " + request.getHeader("Authorization"));
        System.out.println("Origin头: " + request.getHeader("Origin"));
        System.out.println("User-Agent: " + request.getHeader("User-Agent"));
            System.out.println("==========================");
        
        // 打印所有请求头
        System.out.println("=== 所有请求头 ===");
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            System.out.println(headerName + ": " + request.getHeader(headerName));
        }
        System.out.println("================");
        
        // 处理CORS预检请求
        if ("OPTIONS".equals(method)) {
            System.out.println("处理CORS预检请求: " + requestURI);
            filterChain.doFilter(request, response);
            return;
        }
        // 路径匹配部分修正
        AntPathMatcher matcher = new AntPathMatcher();
        String servletPath = request.getServletPath();
        boolean isLogsPath = matcher.match("/logs/**", servletPath);
        boolean isStatisticsPath = matcher.match("/statistics/**", servletPath);
        boolean hasAuthHeader = request.getHeader("Authorization") != null;
        
        System.out.println("=== 路径匹配调试信息 ===");
        System.out.println("request.getRequestURI(): " + request.getRequestURI());
        System.out.println("request.getServletPath(): " + servletPath);
        System.out.println("request.getContextPath(): " + request.getContextPath());
        System.out.println("request.getRequestURL(): " + request.getRequestURL());
        System.out.println("匹配 /logs/**: " + isLogsPath);
        System.out.println("匹配 /statistics/**: " + isStatisticsPath);
        System.out.println("有Authorization头: " + hasAuthHeader);
        System.out.println("需要认证: " + (isLogsPath || isStatisticsPath));
        System.out.println("应该拦截: " + ((isLogsPath || isStatisticsPath) && !hasAuthHeader));
        System.out.println("==========================");
        
        if ((isLogsPath || isStatisticsPath) && !hasAuthHeader) {
            System.out.println("拦截未授权请求: " + servletPath);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            // 设置CORS头
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
            response.getWriter().write("{\"code\":401,\"message\":\"未授权: 缺少Token\"}");
            return;
        }
        
        // 如果路径匹配但仍有Authorization头，检查token有效性
        if ((isLogsPath || isStatisticsPath) && hasAuthHeader) {
            System.out.println("检查已认证请求的token有效性: " + requestURI);
        }
        
        try {
            // 从请求头中获取Authorization
            final String authHeader = request.getHeader("Authorization");
            
            String username = null;
            String jwt = null;
            
            // 检查Authorization头是否存在且以Bearer开头
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                jwt = authHeader.substring(7); // 去掉"Bearer "前缀
                
                try {
                    username = jwtUtil.getUsernameFromToken(jwt);
                } catch (Exception e) {
                    logger.warn("JWT过滤器: 解析token中的用户名失败: " + e.getMessage());
                }
            } else {
                logger.warn("JWT过滤器: 未找到有效的Authorization头");
            }
            
            // 如果用户名不为空且当前没有认证信息
            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                
                // 验证token是否有效
                if (jwtUtil.validateToken(jwt) && !jwtUtil.isTokenExpired(jwt)) {
                    
                    try {
                        // 加载用户详情
                        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                        
                        // 创建认证token
                        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                userDetails, null, userDetails.getAuthorities());
                        
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        
                        // 设置认证信息到SecurityContext
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                    } catch (Exception e) {
                        logger.error("JWT过滤器: 加载用户详情失败: " + e.getMessage());
                    }
                } else {
                    logger.warn("JWT过滤器: token验证失败或已过期");
                }
            } else if (username == null) {
                logger.warn("JWT过滤器: 用户名为空，跳过认证");
            } else {
                logger.warn("JWT过滤器: 已存在认证信息，跳过设置");
            }
        } catch (Exception e) {
            logger.error("JWT认证过滤器处理失败: " + e.getMessage(), e);
        }
        
        // 添加响应头调试信息
        System.out.println("=== JWT过滤器响应信息 ===");
        System.out.println("响应状态码: " + response.getStatus());
        System.out.println("Access-Control-Allow-Origin: " + response.getHeader("Access-Control-Allow-Origin"));
        System.out.println("Access-Control-Allow-Methods: " + response.getHeader("Access-Control-Allow-Methods"));
        System.out.println("Access-Control-Allow-Headers: " + response.getHeader("Access-Control-Allow-Headers"));
        System.out.println("==========================");
        
        filterChain.doFilter(request, response);
    }
} 