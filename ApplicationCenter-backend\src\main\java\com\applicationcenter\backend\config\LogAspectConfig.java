package com.applicationcenter.backend.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 日志切面配置类
 * 启用AOP功能并配置切面行为
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class LogAspectConfig {
    
    // 默认配置，Spring Boot会自动配置AOP
    // 如果需要自定义配置，可以在这里添加Bean定义
    
} 