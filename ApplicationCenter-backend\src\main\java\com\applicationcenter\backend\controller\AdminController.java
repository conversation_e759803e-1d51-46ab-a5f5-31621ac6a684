package com.applicationcenter.backend.controller;

import com.applicationcenter.backend.annotation.LogOperation;
import com.applicationcenter.backend.model.AdminUser;
import com.applicationcenter.backend.service.AdminUserService;
import com.applicationcenter.backend.util.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/admin")
@CrossOrigin(origins = "*")
public class AdminController {
    
    @Autowired
    private AdminUserService adminUserService;
    
    /**
     * 管理员登录
     * 
     * @param loginRequest 登录请求
     * @return 登录结果
     */
    @PostMapping("/login")
    @LogOperation(description = "管理员登录", module = "管理员管理", level = LogOperation.LogLevel.INFO)
    public ApiResponse<Object> login(@RequestBody Map<String, String> loginRequest) {
        String username = loginRequest.get("username");
        String password = loginRequest.get("password");
        
        if (username == null || password == null) {
            return ApiResponse.error("用户名和密码不能为空");
        }
        
        return adminUserService.login(username, password);
    }
    
    /**
     * 创建管理员
     * 
     * @param adminUser 管理员信息
     * @return 创建结果
     */
    @PostMapping("/users")
    @LogOperation(description = "创建管理员", module = "管理员管理", level = LogOperation.LogLevel.INFO)
    public ApiResponse<Object> createAdmin(@RequestBody AdminUser adminUser) {
        return adminUserService.createAdmin(adminUser);
    }
    
    /**
     * 更新管理员
     * 
     * @param id 管理员ID
     * @param adminUser 管理员信息
     * @return 更新结果
     */
    @PutMapping("/users/{id}")
    @LogOperation(description = "更新管理员", module = "管理员管理", level = LogOperation.LogLevel.INFO)
    public ApiResponse<Object> updateAdmin(@PathVariable Integer id, @RequestBody AdminUser adminUser) {
        adminUser.setId(id);
        return adminUserService.updateAdmin(adminUser);
    }
    
    /**
     * 删除管理员
     * 
     * @param id 管理员ID
     * @return 删除结果
     */
    @DeleteMapping("/users/{id}")
    @LogOperation(description = "删除管理员", module = "管理员管理", level = LogOperation.LogLevel.WARN)
    public ApiResponse<Object> deleteAdmin(@PathVariable Integer id) {
        return adminUserService.deleteAdmin(id);
    }
    
    /**
     * 获取管理员信息
     * 
     * @param id 管理员ID
     * @return 管理员信息
     */
    @GetMapping("/users/{id}")
    @LogOperation(description = "获取管理员信息", module = "管理员管理", level = LogOperation.LogLevel.INFO)
    public ApiResponse<Object> getAdmin(@PathVariable Integer id) {
        AdminUser admin = adminUserService.findById(id);
        if (admin == null) {
            return ApiResponse.error("管理员不存在");
        }
        
        // 不返回密码信息
        admin.setPassword(null);
        return ApiResponse.success(admin);
    }
    
    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ApiResponse<Object> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("message", "ApplicationCenter Backend is running");
        return ApiResponse.success(data);
    }
} 