package com.applicationcenter.backend.controller;

import com.applicationcenter.backend.annotation.LogOperation;
import com.applicationcenter.backend.model.Application;
import com.applicationcenter.backend.service.ApplicationService;
import com.applicationcenter.backend.util.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 应用管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/applications")
@CrossOrigin(origins = "*")
public class ApplicationController {
    
    @Autowired
    private ApplicationService applicationService;
    
    /**
     * 创建应用
     * 
     * @param application 应用信息
     * @return 创建结果
     */
    @PostMapping
    @LogOperation(description = "创建应用", module = "应用管理", level = LogOperation.LogLevel.INFO)
    public ApiResponse<Object> createApplication(@RequestBody Application application) {
        // 获取当前登录的管理员ID
        Integer adminId = getCurrentAdminId();
        if (adminId == null) {
            return ApiResponse.error("未找到管理员信息");
        }
        
        return applicationService.createApplication(application, adminId);
    }
    
    /**
     * 更新应用
     * 
     * @param id 应用ID
     * @param application 应用信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @LogOperation(description = "更新应用", module = "应用管理", level = LogOperation.LogLevel.INFO)
    public ApiResponse<Object> updateApplication(@PathVariable Integer id, @RequestBody Application application) {
        return applicationService.updateApplication(id, application);
    }
    
    /**
     * 删除应用
     * 
     * @param id 应用ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @LogOperation(description = "删除应用", module = "应用管理", level = LogOperation.LogLevel.WARN)
    public ApiResponse<Object> deleteApplication(@PathVariable Integer id) {
        return applicationService.deleteApplication(id);
    }
    
    /**
     * 获取应用详情
     * 
     * @param id 应用ID
     * @return 应用详情
     */
    @GetMapping("/{id}")
    @LogOperation(description = "获取应用详情", module = "应用管理", level = LogOperation.LogLevel.INFO)
    public ApiResponse<Object> getApplication(@PathVariable Integer id) {
        return applicationService.getApplication(id);
    }
    
    /**
     * 获取应用列表（分页）
     * 
     * @param page 页码（默认0）
     * @param size 每页大小（默认10）
     * @param sort 排序字段（默认id）
     * @param direction 排序方向（默认desc）
     * @return 应用列表
     */
    @GetMapping
    @LogOperation(description = "获取应用列表", module = "应用管理", level = LogOperation.LogLevel.INFO)
    public ApiResponse<Object> getApplicationList(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "desc") String direction) {
        
        Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? 
            Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        
        return applicationService.getApplicationList(pageable);
    }
    
    /**
     * 获取所有应用列表（不分页）
     * 
     * @return 应用列表
     */
    @GetMapping("/all")
    public ApiResponse<Object> getAllApplications() {
        return applicationService.getAllApplications();
    }
    
    /**
     * 根据平台获取应用列表
     * 
     * @param platform 平台
     * @return 应用列表
     */
    @GetMapping("/platform/{platform}")
    public ApiResponse<Object> getApplicationsByPlatform(@PathVariable String platform) {
        try {
            Application.Platform platformEnum = Application.Platform.valueOf(platform.toUpperCase());
            return applicationService.getApplicationsByPlatform(platformEnum);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("无效的平台类型");
        }
    }
    
    /**
     * 根据分类获取应用列表
     * 
     * @param category 分类
     * @return 应用列表
     */
    @GetMapping("/category/{category}")
    public ApiResponse<Object> getApplicationsByCategory(@PathVariable String category) {
        return applicationService.getApplicationsByCategory(category);
    }
    
    /**
     * 根据开发者获取应用列表
     * 
     * @param developer 开发者
     * @return 应用列表
     */
    @GetMapping("/developer/{developer}")
    public ApiResponse<Object> getApplicationsByDeveloper(@PathVariable String developer) {
        return applicationService.getApplicationsByDeveloper(developer);
    }
    
    /**
     * 搜索应用
     * 
     * @param keyword 关键词
     * @return 应用列表
     */
    @GetMapping("/search")
    @LogOperation(description = "搜索应用", module = "应用管理", level = LogOperation.LogLevel.INFO)
    public ApiResponse<Object> searchApplications(@RequestParam String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return ApiResponse.error("搜索关键词不能为空");
        }
        return applicationService.searchApplications(keyword.trim());
    }
    
    /**
     * 启用/禁用应用
     * 
     * @param id 应用ID
     * @param status 状态（1:启用, 0:禁用）
     * @return 操作结果
     */
    @PatchMapping("/{id}/status")
    public ApiResponse<Object> updateApplicationStatus(
            @PathVariable Integer id, 
            @RequestParam Integer status) {
        
        if (status == null || (status != 0 && status != 1)) {
            return ApiResponse.error("状态值无效，只能是0或1");
        }
        
        return applicationService.updateApplicationStatus(id, status);
    }
    
    /**
     * 获取应用统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Object> getApplicationStatistics() {
        return applicationService.getApplicationStatistics();
    }
    
    /**
     * 检查包名是否存在
     * 
     * @param packageName 包名
     * @return 检查结果
     */
    @GetMapping("/check-package")
    public ApiResponse<Object> checkPackageName(@RequestParam String packageName) {
        if (packageName == null || packageName.trim().isEmpty()) {
            return ApiResponse.error("包名不能为空");
        }
        
        boolean exists = applicationService.existsByPackageName(packageName.trim());
        Map<String, Object> data = new HashMap<>();
        data.put("exists", exists);
        data.put("packageName", packageName.trim());
        
        return ApiResponse.success("检查完成", data);
    }
    
    /**
     * 获取公开的应用列表（无需认证）
     * 
     * @return 公开的应用列表
     */
    @GetMapping("/public")
    public ApiResponse<Object> getPublicApplications() {
        return applicationService.getPublicApplications();
    }
    
    /**
     * 获取平台列表
     * 
     * @return 平台列表
     */
    @GetMapping("/platforms")
    @LogOperation(description = "获取平台列表", module = "应用管理", level = LogOperation.LogLevel.INFO)
    public ApiResponse<Object> getPlatforms() {
        Application.Platform[] platforms = Application.Platform.values();
        Map<String, String> platformMap = new HashMap<>();
        
        for (Application.Platform platform : platforms) {
            platformMap.put(platform.name(), platform.getValue());
        }
        
        return ApiResponse.success("获取平台列表成功", platformMap);
    }
    
    /**
     * 获取当前登录的管理员ID
     * 
     * @return 管理员ID
     */
    private Integer getCurrentAdminId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() != null) {
                // 这里需要根据你的UserDetails实现来获取管理员ID
                // 暂时返回默认值，实际使用时需要从UserDetails中提取
                return 1; // 默认管理员ID
            }
        } catch (Exception e) {
            // 记录日志但不抛出异常
        }
        return null;
    }
} 