package com.applicationcenter.backend.controller;

import com.applicationcenter.backend.model.ErrorLog;
import com.applicationcenter.backend.model.OperationLog;
import com.applicationcenter.backend.service.ErrorLogService;
import com.applicationcenter.backend.service.OperationLogService;
import com.applicationcenter.backend.util.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import org.springframework.http.ResponseEntity;

/**
 * 日志管理Controller
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/logs")
@CrossOrigin(origins = "*")
public class LogController {

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private ErrorLogService errorLogService;

    // 工具方法：分页参数校验
    private void validatePageParams(int page, int size) {
        if (page < 0 || size < 1 || size > 100) {
            throw new IllegalArgumentException("分页参数不合法: page >= 0, 1 <= size <= 100");
        }
    }
    // 工具方法：时间范围校验
    private void validateTimeRange(java.time.LocalDateTime start, java.time.LocalDateTime end) {
        if (start == null || end == null || !start.isBefore(end)) {
            throw new IllegalArgumentException("时间范围不合法: startTime 必须早于 endTime");
        }
    }

    /**
     * 获取操作日志列表
     */
    @GetMapping("/operations")
    public ApiResponse<Page<OperationLog>> getOperationLogs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            validatePageParams(page, size);
            Pageable pageable = PageRequest.of(page, size);
            Page<OperationLog> logs = operationLogService.getLogs(pageable);
            return ApiResponse.success(logs);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("获取操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID获取操作日志
     */
    @GetMapping("/operations/user/{userId}")
    public ApiResponse<Page<OperationLog>> getOperationLogsByUser(
            @PathVariable Integer userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<OperationLog> logs = operationLogService.getLogsByUserId(userId, pageable);
            return ApiResponse.success(logs);
        } catch (Exception e) {
            return ApiResponse.error("获取用户操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 根据模块获取操作日志
     */
    @GetMapping("/operations/module/{module}")
    public ApiResponse<Page<OperationLog>> getOperationLogsByModule(
            @PathVariable String module,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<OperationLog> logs = operationLogService.getLogsByModule(module, pageable);
            return ApiResponse.success(logs);
        } catch (Exception e) {
            return ApiResponse.error("获取模块操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 根据时间范围获取操作日志
     */
    @GetMapping("/operations/time-range")
    public ApiResponse<Page<OperationLog>> getOperationLogsByTimeRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) java.time.LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) java.time.LocalDateTime endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            validatePageParams(page, size);
            validateTimeRange(startTime, endTime);
            Pageable pageable = PageRequest.of(page, size);
            Page<OperationLog> logs = operationLogService.getLogsByTimeRange(startTime, endTime, pageable);
            return ApiResponse.success(logs);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("获取时间范围操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取最活跃用户
     */
    @GetMapping("/operations/most-active-users")
    public ApiResponse<List<Object[]>> getMostActiveUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            List<Object[]> users = operationLogService.getMostActiveUsers(pageable);
            return ApiResponse.success(users);
        } catch (Exception e) {
            return ApiResponse.error("获取最活跃用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取最常用操作
     */
    @GetMapping("/operations/most-common")
    public ApiResponse<List<Object[]>> getMostCommonOperations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            List<Object[]> operations = operationLogService.getMostCommonOperations(pageable);
            return ApiResponse.success(operations);
        } catch (Exception e) {
            return ApiResponse.error("获取最常用操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取最活跃模块
     */
    @GetMapping("/operations/most-active-modules")
    public ApiResponse<List<Object[]>> getMostActiveModules(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            List<Object[]> modules = operationLogService.getMostActiveModules(pageable);
            return ApiResponse.success(modules);
        } catch (Exception e) {
            return ApiResponse.error("获取最活跃模块失败: " + e.getMessage());
        }
    }

    /**
     * 获取错误日志列表
     */
    @GetMapping("/errors")
    public ApiResponse<Page<ErrorLog>> getErrorLogs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ErrorLog> logs = errorLogService.getLogs(pageable);
            return ApiResponse.success(logs);
        } catch (Exception e) {
            return ApiResponse.error("获取错误日志失败: " + e.getMessage());
        }
    }

    /**
     * 根据错误类型获取错误日志
     */
    @GetMapping("/errors/type/{errorType}")
    public ApiResponse<Page<ErrorLog>> getErrorLogsByType(
            @PathVariable String errorType,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ErrorLog> logs = errorLogService.getLogsByErrorType(errorType, pageable);
            return ApiResponse.success(logs);
        } catch (Exception e) {
            return ApiResponse.error("获取错误类型日志失败: " + e.getMessage());
        }
    }

    /**
     * 根据模块获取错误日志
     */
    @GetMapping("/errors/module/{module}")
    public ApiResponse<Page<ErrorLog>> getErrorLogsByModule(
            @PathVariable String module,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ErrorLog> logs = errorLogService.getLogsByModule(module, pageable);
            return ApiResponse.success(logs);
        } catch (Exception e) {
            return ApiResponse.error("获取模块错误日志失败: " + e.getMessage());
        }
    }

    /**
     * 根据严重程度获取错误日志
     */
    @GetMapping("/errors/severity/{severity}")
    public ApiResponse<Page<ErrorLog>> getErrorLogsBySeverity(
            @PathVariable String severity,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ErrorLog> logs = errorLogService.getLogsBySeverity(severity, pageable);
            return ApiResponse.success(logs);
        } catch (Exception e) {
            return ApiResponse.error("获取严重程度错误日志失败: " + e.getMessage());
        }
    }

    /**
     * 根据时间范围获取错误日志
     */
    @GetMapping("/errors/time-range")
    public ApiResponse<Page<ErrorLog>> getErrorLogsByTimeRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) java.time.LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) java.time.LocalDateTime endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            validatePageParams(page, size);
            validateTimeRange(startTime, endTime);
            Pageable pageable = PageRequest.of(page, size);
            Page<ErrorLog> logs = errorLogService.getLogsByTimeRange(startTime, endTime, pageable);
            return ApiResponse.success(logs);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("获取时间范围错误日志失败: " + e.getMessage());
        }
    }

    /**
     * 根据是否已解决获取错误日志
     */
    @GetMapping("/errors/resolved/{resolved}")
    public ApiResponse<Page<ErrorLog>> getErrorLogsByResolved(
            @PathVariable Boolean resolved,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ErrorLog> logs = errorLogService.getLogsByResolved(resolved, pageable);
            return ApiResponse.success(logs);
        } catch (Exception e) {
            return ApiResponse.error("获取解决状态错误日志失败: " + e.getMessage());
        }
    }

    /**
     * 标记错误为已解决
     */
    @PutMapping("/errors/{logId}/resolve")
    public ApiResponse<ErrorLog> markErrorAsResolved(
            @PathVariable Long logId,
            @RequestParam Integer resolvedByUserId) {
        try {
            ErrorLog log = errorLogService.markAsResolved(logId, resolvedByUserId);
            if (log != null) {
                return ApiResponse.success(log);
            } else {
                return ApiResponse.error("错误日志不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error("标记错误为已解决失败: " + e.getMessage());
        }
    }

    /**
     * 获取最常见错误类型
     */
    @GetMapping("/errors/most-common-types")
    public ApiResponse<List<Object[]>> getMostCommonErrorTypes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            List<Object[]> types = errorLogService.getMostCommonErrorTypes(pageable);
            return ApiResponse.success(types);
        } catch (Exception e) {
            return ApiResponse.error("获取最常见错误类型失败: " + e.getMessage());
        }
    }

    /**
     * 获取最容易出错的模块
     */
    @GetMapping("/errors/most-error-prone-modules")
    public ApiResponse<List<Object[]>> getMostErrorProneModules(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            List<Object[]> modules = errorLogService.getMostErrorProneModules(pageable);
            return ApiResponse.success(modules);
        } catch (Exception e) {
            return ApiResponse.error("获取最容易出错模块失败: " + e.getMessage());
        }
    }

    /**
     * 获取错误严重程度分布
     */
    @GetMapping("/errors/severity-distribution")
    public ApiResponse<List<Object[]>> getErrorSeverityDistribution() {
        try {
            List<Object[]> distribution = errorLogService.getSeverityDistribution();
            return ApiResponse.success(distribution);
        } catch (Exception e) {
            return ApiResponse.error("获取错误严重程度分布失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近的严重错误
     */
    @GetMapping("/errors/recent-critical")
    public ApiResponse<List<ErrorLog>> getRecentCriticalErrors(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            List<ErrorLog> errors = errorLogService.getRecentCriticalErrors(pageable);
            return ApiResponse.success(errors);
        } catch (Exception e) {
            return ApiResponse.error("获取最近严重错误失败: " + e.getMessage());
        }
    }

    /**
     * 删除指定时间之前的操作日志
     */
    @DeleteMapping("/operations/cleanup")
    public ApiResponse<String> cleanupOperationLogs(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beforeTime) {
        try {
            operationLogService.deleteLogsBefore(beforeTime);
            return ApiResponse.success("操作日志清理成功");
        } catch (Exception e) {
            return ApiResponse.error("操作日志清理失败: " + e.getMessage());
        }
    }

    /**
     * 删除指定时间之前的错误日志
     */
    @DeleteMapping("/errors/cleanup")
    public ApiResponse<String> cleanupErrorLogs(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beforeTime) {
        try {
            errorLogService.deleteLogsBefore(beforeTime);
            return ApiResponse.success("错误日志清理成功");
        } catch (Exception e) {
            return ApiResponse.error("错误日志清理失败: " + e.getMessage());
        }
    }

    /**
     * 删除已解决的错误日志
     */
    @DeleteMapping("/errors/cleanup-resolved")
    public ApiResponse<String> cleanupResolvedErrorLogs() {
        try {
            errorLogService.deleteResolvedLogs();
            return ApiResponse.success("已解决错误日志清理成功");
        } catch (Exception e) {
            return ApiResponse.error("已解决错误日志清理失败: " + e.getMessage());
        }
    }
} 