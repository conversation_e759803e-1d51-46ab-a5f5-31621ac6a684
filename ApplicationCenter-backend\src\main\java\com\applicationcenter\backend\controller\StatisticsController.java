package com.applicationcenter.backend.controller;

import com.applicationcenter.backend.service.StatisticsService;
import com.applicationcenter.backend.util.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 统计管理Controller
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/statistics")
@CrossOrigin(origins = "*")
public class StatisticsController {

    @Autowired
    private StatisticsService statisticsService;

    /**
     * 获取系统概览统计
     */
    @GetMapping("/overview")
    public ApiResponse<Map<String, Object>> getSystemOverview() {
        try {
            Map<String, Object> overview = statisticsService.getSystemOverview();
            return ApiResponse.success(overview);
        } catch (Exception e) {
            return ApiResponse.error("获取系统概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取应用统计信息
     */
    @GetMapping("/applications")
    public ApiResponse<Map<String, Object>> getApplicationStatistics() {
        try {
            Map<String, Object> stats = statisticsService.getApplicationStatistics();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error("获取应用统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本统计信息
     */
    @GetMapping("/versions")
    public ApiResponse<Map<String, Object>> getVersionStatistics() {
        try {
            Map<String, Object> stats = statisticsService.getVersionStatistics();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error("获取版本统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户活跃度统计
     */
    @GetMapping("/user-activity")
    public ApiResponse<Map<String, Object>> getUserActivityStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null || endTime == null || !startTime.isBefore(endTime)) {
                return ApiResponse.error("时间范围不合法: startTime 必须早于 endTime");
            }
            Map<String, Object> stats = statisticsService.getUserActivityStatistics(startTime, endTime);
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error("获取用户活跃度统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取操作日志统计
     */
    @GetMapping("/operation-logs")
    public ApiResponse<Map<String, Object>> getOperationLogStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null || endTime == null || !startTime.isBefore(endTime)) {
                return ApiResponse.error("时间范围不合法: startTime 必须早于 endTime");
            }
            Map<String, Object> stats = statisticsService.getOperationLogStatistics(startTime, endTime);
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error("获取操作日志统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取错误日志统计
     */
    @GetMapping("/error-logs")
    public ApiResponse<Map<String, Object>> getErrorLogStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null || endTime == null || !startTime.isBefore(endTime)) {
                return ApiResponse.error("时间范围不合法: startTime 必须早于 endTime");
            }
            Map<String, Object> stats = statisticsService.getErrorLogStatistics(startTime, endTime);
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error("获取错误日志统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件上传统计
     */
    @GetMapping("/file-uploads")
    public ApiResponse<Map<String, Object>> getFileUploadStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null || endTime == null || !startTime.isBefore(endTime)) {
                return ApiResponse.error("时间范围不合法: startTime 必须早于 endTime");
            }
            Map<String, Object> stats = statisticsService.getFileUploadStatistics(startTime, endTime);
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error("获取文件上传统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取平台分布统计
     */
    @GetMapping("/platform-distribution")
    public ApiResponse<List<Map<String, Object>>> getPlatformDistribution() {
        try {
            List<Map<String, Object>> distribution = statisticsService.getPlatformDistribution();
            return ApiResponse.success(distribution);
        } catch (Exception e) {
            return ApiResponse.error("获取平台分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本状态分布统计
     */
    @GetMapping("/version-status-distribution")
    public ApiResponse<List<Map<String, Object>>> getVersionStatusDistribution() {
        try {
            List<Map<String, Object>> distribution = statisticsService.getVersionStatusDistribution();
            return ApiResponse.success(distribution);
        } catch (Exception e) {
            return ApiResponse.error("获取版本状态分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取错误严重程度分布统计
     */
    @GetMapping("/error-severity-distribution")
    public ApiResponse<List<Map<String, Object>>> getErrorSeverityDistribution() {
        try {
            List<Map<String, Object>> distribution = statisticsService.getErrorSeverityDistribution();
            return ApiResponse.success(distribution);
        } catch (Exception e) {
            return ApiResponse.error("获取错误严重程度分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取每日活跃用户统计
     */
    @GetMapping("/daily-active-users")
    public ApiResponse<List<Map<String, Object>>> getDailyActiveUsers(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null || endTime == null || !startTime.isBefore(endTime)) {
                return ApiResponse.error("时间范围不合法: startTime 必须早于 endTime");
            }
            List<Map<String, Object>> data = statisticsService.getDailyActiveUsers(startTime, endTime);
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取每日活跃用户统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取每日操作次数统计
     */
    @GetMapping("/daily-operations")
    public ApiResponse<List<Map<String, Object>>> getDailyOperations(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null || endTime == null || !startTime.isBefore(endTime)) {
                return ApiResponse.error("时间范围不合法: startTime 必须早于 endTime");
            }
            List<Map<String, Object>> data = statisticsService.getDailyOperations(startTime, endTime);
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取每日操作次数统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取每日错误次数统计
     */
    @GetMapping("/daily-errors")
    public ApiResponse<List<Map<String, Object>>> getDailyErrors(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null || endTime == null || !startTime.isBefore(endTime)) {
                return ApiResponse.error("时间范围不合法: startTime 必须早于 endTime");
            }
            List<Map<String, Object>> data = statisticsService.getDailyErrors(startTime, endTime);
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取每日错误次数统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取综合统计报告
     */
    @GetMapping("/comprehensive-report")
    public ApiResponse<Map<String, Object>> getComprehensiveReport(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null || endTime == null || !startTime.isBefore(endTime)) {
                return ApiResponse.error("时间范围不合法: startTime 必须早于 endTime");
            }
            Map<String, Object> report = new java.util.HashMap<>();
            
            // 系统概览
            report.put("systemOverview", statisticsService.getSystemOverview());
            
            // 应用统计
            report.put("applicationStatistics", statisticsService.getApplicationStatistics());
            
            // 版本统计
            report.put("versionStatistics", statisticsService.getVersionStatistics());
            
            // 用户活跃度统计
            report.put("userActivityStatistics", statisticsService.getUserActivityStatistics(startTime, endTime));
            
            // 操作日志统计
            report.put("operationLogStatistics", statisticsService.getOperationLogStatistics(startTime, endTime));
            
            // 错误日志统计
            report.put("errorLogStatistics", statisticsService.getErrorLogStatistics(startTime, endTime));
            
            // 文件上传统计
            report.put("fileUploadStatistics", statisticsService.getFileUploadStatistics(startTime, endTime));
            
            // 分布统计
            report.put("platformDistribution", statisticsService.getPlatformDistribution());
            report.put("versionStatusDistribution", statisticsService.getVersionStatusDistribution());
            report.put("errorSeverityDistribution", statisticsService.getErrorSeverityDistribution());
            
            return ApiResponse.success(report);
        } catch (Exception e) {
            return ApiResponse.error("获取综合统计报告失败: " + e.getMessage());
        }
    }
} 