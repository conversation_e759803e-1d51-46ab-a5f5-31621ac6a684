package com.applicationcenter.backend.controller;

import com.applicationcenter.backend.util.ApiResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/test")
public class TestController {
    
    /**
     * 测试接口
     * 
     * @return 测试结果
     */
    @GetMapping("/hello")
    public ApiResponse<Object> hello() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "Hello ApplicationCenter Backend!");
        data.put("timestamp", System.currentTimeMillis());
        return ApiResponse.success(data);
    }
    
    /**
     * 数据库连接测试
     * 
     * @return 测试结果
     */
    @GetMapping("/db")
    public ApiResponse<Object> testDatabase() {
        try {
            // 这里可以添加数据库连接测试逻辑
            Map<String, Object> data = new HashMap<>();
            data.put("status", "Database connection OK");
            data.put("timestamp", System.currentTimeMillis());
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("Database connection failed: " + e.getMessage());
        }
    }
    
    /**
     * 测试认证
     * 
     * @return 认证信息
     */
    @GetMapping("/auth")
    public ApiResponse<Object> testAuth() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Map<String, Object> data = new HashMap<>();
        data.put("message", "认证成功");
        data.put("username", authentication.getName());
        data.put("authorities", authentication.getAuthorities().toString());
        data.put("timestamp", System.currentTimeMillis());
        return ApiResponse.success(data);
    }
} 