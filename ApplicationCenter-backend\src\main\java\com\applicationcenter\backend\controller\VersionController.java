package com.applicationcenter.backend.controller;

import com.applicationcenter.backend.annotation.LogOperation;
import com.applicationcenter.backend.model.Version;
import com.applicationcenter.backend.service.VersionService;
import com.applicationcenter.backend.util.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import jakarta.servlet.http.HttpServletRequest;

import java.util.Map;

/**
 * 版本管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/versions")
@CrossOrigin(origins = "*")
public class VersionController {
    
    @Autowired
    private VersionService versionService;
    
    /**
     * 创建版本
     * 
     * @param version 版本信息
     * @return 创建结果
     */
    @PostMapping
    @LogOperation(description = "创建版本", module = "版本管理", level = LogOperation.LogLevel.INFO)
    public ResponseEntity<ApiResponse<Object>> createVersion(@RequestBody Version version) {
        try {
            // 验证必要参数
            if (version == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error("版本信息不能为空"));
            }
            
            if (version.getApplication() == null || version.getApplication().getId() == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error("应用ID不能为空"));
            }
            
            if (version.getVersionCode() == null || version.getVersionCode().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(ApiResponse.error("版本号不能为空"));
            }
            
            if (version.getVersionName() == null || version.getVersionName().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(ApiResponse.error("版本名称不能为空"));
            }
            
            // TODO: 从JWT中获取管理员ID
            Integer adminId = 1; // 临时使用固定值
            
            ApiResponse<Object> response = versionService.createVersion(version, adminId);
            
            if ("200".equals(response.getCode())) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.error("创建版本时发生错误: " + e.getMessage()));
        }
    }
    
    /**
     * 上传版本文件
     * 
     * @param versionId 版本ID
     * @param file 文件
     * @return 上传结果
     */
    @PostMapping("/{versionId}/upload")
    @LogOperation(description = "上传版本文件", module = "版本管理", level = LogOperation.LogLevel.INFO)
    public ResponseEntity<ApiResponse<Object>> uploadVersionFile(
            @PathVariable Integer versionId,
            @RequestParam("file") MultipartFile file,
            HttpServletRequest request) {
        
        // 添加调试日志
        System.out.println("=== 文件上传调试信息 ===");
        System.out.println("版本ID: " + versionId);
        System.out.println("文件名: " + (file != null ? file.getOriginalFilename() : "null"));
        System.out.println("文件大小: " + (file != null ? file.getSize() : "null"));
        System.out.println("Content-Type: " + (file != null ? file.getContentType() : "null"));
        System.out.println("请求URL: " + request.getRequestURL());
        System.out.println("Authorization头: " + request.getHeader("Authorization"));
        System.out.println("Content-Type头: " + request.getContentType());
        System.out.println("========================");
        
        // TODO: 从JWT中获取管理员ID
        Integer adminId = 1; // 临时使用固定值
        return ResponseEntity.ok(versionService.uploadVersionFile(versionId, file, adminId));
    }
    
    /**
     * 更新版本信息
     * 
     * @param id 版本ID
     * @param version 版本信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @LogOperation(description = "更新版本", module = "版本管理", level = LogOperation.LogLevel.INFO)
    public ResponseEntity<ApiResponse<Object>> updateVersion(
            @PathVariable Integer id,
            @RequestBody Version version) {
        return ResponseEntity.ok(versionService.updateVersion(id, version));
    }
    
    /**
     * 删除版本
     * 
     * @param id 版本ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @LogOperation(description = "删除版本", module = "版本管理", level = LogOperation.LogLevel.WARN)
    public ResponseEntity<ApiResponse<Object>> deleteVersion(@PathVariable Integer id) {
        return ResponseEntity.ok(versionService.deleteVersion(id));
    }
    
    /**
     * 获取版本详情
     * 
     * @param id 版本ID
     * @return 版本详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Object>> getVersion(@PathVariable Integer id) {
        return ResponseEntity.ok(versionService.getVersion(id));
    }
    
    /**
     * 获取应用的所有版本列表
     * 
     * @param applicationId 应用ID
     * @return 版本列表
     */
    @GetMapping("/application/{applicationId}")
    public ResponseEntity<ApiResponse<Object>> getVersionsByApplication(@PathVariable Integer applicationId) {
        return ResponseEntity.ok(versionService.getVersionsByApplication(applicationId));
    }
    
    /**
     * 获取应用的所有版本列表（分页）
     * 
     * @param applicationId 应用ID
     * @param page 页码
     * @param size 每页大小
     * @return 版本列表
     */
    @GetMapping("/application/{applicationId}/page")
    public ResponseEntity<ApiResponse<Object>> getVersionsByApplication(
            @PathVariable Integer applicationId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        return ResponseEntity.ok(versionService.getVersionsByApplication(applicationId, pageable));
    }
    
    /**
     * 获取应用的最新发布版本
     * 
     * @param applicationId 应用ID
     * @return 最新版本
     */
    @GetMapping("/application/{applicationId}/latest")
    public ResponseEntity<ApiResponse<Object>> getLatestPublishedVersion(@PathVariable Integer applicationId) {
        return ResponseEntity.ok(versionService.getLatestPublishedVersion(applicationId));
    }
    
    /**
     * 搜索版本
     * 
     * @param keyword 关键词
     * @return 版本列表
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Object>> searchVersions(@RequestParam String keyword) {
        return ResponseEntity.ok(versionService.searchVersions(keyword));
    }
    
    /**
     * 搜索版本（分页）
     * 
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 版本列表
     */
    @GetMapping("/search/page")
    public ResponseEntity<ApiResponse<Object>> searchVersions(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        return ResponseEntity.ok(versionService.searchVersions(keyword, pageable));
    }
    
    /**
     * 发布版本
     * 
     * @param id 版本ID
     * @return 发布结果
     */
    @PatchMapping("/{id}/publish")
    @LogOperation(description = "发布版本", module = "版本管理", level = LogOperation.LogLevel.INFO)
    public ResponseEntity<ApiResponse<Object>> publishVersion(@PathVariable Integer id) {
        return ResponseEntity.ok(versionService.publishVersion(id));
    }
    
    /**
     * 下架版本
     * 
     * @param id 版本ID
     * @return 下架结果
     */
    @PatchMapping("/{id}/deprecate")
    public ResponseEntity<ApiResponse<Object>> deprecateVersion(@PathVariable Integer id) {
        return ResponseEntity.ok(versionService.deprecateVersion(id));
    }
    
    /**
     * 更新版本状态
     * 
     * @param id 版本ID
     * @param status 状态
     * @return 更新结果
     */
    @PatchMapping("/{id}/status")
    public ResponseEntity<ApiResponse<Object>> updateVersionStatus(
            @PathVariable Integer id,
            @RequestBody Map<String, String> request) {
        String statusStr = request.get("status");
        Version.Status status = Version.Status.valueOf(statusStr.toUpperCase());
        return ResponseEntity.ok(versionService.updateVersionStatus(id, status));
    }
    
    /**
     * 设置强制更新
     * 
     * @param id 版本ID
     * @param request 请求参数
     * @return 设置结果
     */
    @PatchMapping("/{id}/forced-update")
    public ResponseEntity<ApiResponse<Object>> setForcedUpdate(
            @PathVariable Integer id,
            @RequestBody Map<String, Boolean> request) {
        Boolean isForcedUpdate = request.get("isForcedUpdate");
        return ResponseEntity.ok(versionService.setForcedUpdate(id, isForcedUpdate));
    }
    
    /**
     * 获取版本统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @LogOperation(description = "获取版本统计", module = "版本管理", level = LogOperation.LogLevel.INFO)
    public ResponseEntity<ApiResponse<Object>> getVersionStatistics() {
        return ResponseEntity.ok(versionService.getVersionStatistics());
    }
    
    /**
     * 获取应用的版本统计信息
     * 
     * @param applicationId 应用ID
     * @return 统计信息
     */
    @GetMapping("/statistics/application/{applicationId}")
    public ResponseEntity<ApiResponse<Object>> getVersionStatisticsByApplication(@PathVariable Integer applicationId) {
        return ResponseEntity.ok(versionService.getVersionStatisticsByApplication(applicationId));
    }
    
    /**
     * 获取版本下载链接
     * 
     * @param id 版本ID
     * @return 下载链接
     */
    @GetMapping("/{id}/download-url")
    public ResponseEntity<ApiResponse<Object>> getDownloadUrl(@PathVariable Integer id) {
        return ResponseEntity.ok(versionService.getDownloadUrl(id));
    }
    
    /**
     * 检查更新
     * 
     * @param applicationId 应用ID
     * @param currentVersion 当前版本号
     * @return 更新信息
     */
    @GetMapping("/check-update")
    public ResponseEntity<ApiResponse<Object>> checkForUpdate(
            @RequestParam Integer applicationId,
            @RequestParam String currentVersion) {
        return ResponseEntity.ok(versionService.checkForUpdate(applicationId, currentVersion));
    }
    
    /**
     * 获取应用的强制更新版本
     * 
     * @param applicationId 应用ID
     * @return 强制更新版本
     */
    @GetMapping("/forced-update/{applicationId}")
    public ResponseEntity<ApiResponse<Object>> getForcedUpdateVersion(@PathVariable Integer applicationId) {
        return ResponseEntity.ok(versionService.getForcedUpdateVersion(applicationId));
    }
    
    /**
     * 根据状态获取版本列表
     * 
     * @param status 状态
     * @return 版本列表
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<ApiResponse<Object>> getVersionsByStatus(@PathVariable String status) {
        Version.Status versionStatus = Version.Status.valueOf(status.toUpperCase());
        return ResponseEntity.ok(versionService.getVersionsByStatus(versionStatus));
    }
    
    /**
     * 根据状态获取版本列表（分页）
     * 
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 版本列表
     */
    @GetMapping("/status/{status}/page")
    public ResponseEntity<ApiResponse<Object>> getVersionsByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Version.Status versionStatus = Version.Status.valueOf(status.toUpperCase());
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        return ResponseEntity.ok(versionService.getVersionsByStatus(versionStatus, pageable));
    }
} 