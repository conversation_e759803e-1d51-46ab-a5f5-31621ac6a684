package com.applicationcenter.backend.repository;

import com.applicationcenter.backend.model.AdminUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 管理员数据访问层
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public interface AdminUserRepository extends JpaRepository<AdminUser, Integer> {
    
    /**
     * 根据用户名查找管理员
     * 
     * @param username 用户名
     * @return 管理员信息
     */
    Optional<AdminUser> findByUsername(String username);
    
    /**
     * 根据用户名和状态查找管理员
     * 
     * @param username 用户名
     * @param status 状态
     * @return 管理员信息
     */
    Optional<AdminUser> findByUsernameAndStatus(String username, Integer status);
    
    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);
} 