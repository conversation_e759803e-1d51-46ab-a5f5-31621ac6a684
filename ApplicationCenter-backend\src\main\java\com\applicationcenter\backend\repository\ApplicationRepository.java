package com.applicationcenter.backend.repository;

import com.applicationcenter.backend.model.Application;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 应用数据访问层
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public interface ApplicationRepository extends JpaRepository<Application, Integer> {
    
    /**
     * 根据状态查找应用
     * 
     * @param status 状态
     * @return 应用列表
     */
    List<Application> findByStatus(Integer status);
    
    /**
     * 根据状态查找应用（分页）
     * 
     * @param status 状态
     * @param pageable 分页参数
     * @return 应用分页结果
     */
    Page<Application> findByStatus(Integer status, Pageable pageable);
    
    /**
     * 根据平台查找应用
     * 
     * @param platform 平台
     * @return 应用列表
     */
    List<Application> findByPlatform(Application.Platform platform);
    
    /**
     * 根据平台和状态查找应用
     * 
     * @param platform 平台
     * @param status 状态
     * @return 应用列表
     */
    List<Application> findByPlatformAndStatus(Application.Platform platform, Integer status);
    
    /**
     * 根据包名查找应用
     * 
     * @param packageName 包名
     * @return 应用
     */
    Optional<Application> findByPackageName(String packageName);
    
    /**
     * 根据包名和状态查找应用
     * 
     * @param packageName 包名
     * @param status 状态
     * @return 应用
     */
    Optional<Application> findByPackageNameAndStatus(String packageName, Integer status);
    
    /**
     * 检查包名是否存在
     * 
     * @param packageName 包名
     * @return 是否存在
     */
    boolean existsByPackageName(String packageName);
    
    /**
     * 根据包名和状态检查是否存在
     * 
     * @param packageName 包名
     * @param status 状态
     * @return 是否存在
     */
    boolean existsByPackageNameAndStatus(String packageName, Integer status);
    
    /**
     * 根据分类查找应用
     * 
     * @param category 分类
     * @return 应用列表
     */
    List<Application> findByCategory(String category);
    
    /**
     * 根据开发者查找应用
     * 
     * @param developer 开发者
     * @return 应用列表
     */
    List<Application> findByDeveloper(String developer);
    
    /**
     * 根据创建者查找应用
     * 
     * @param createdBy 创建者ID
     * @return 应用列表
     */
    List<Application> findByCreatedBy(Integer createdBy);
    
    /**
     * 根据名称模糊查询
     * 
     * @param name 应用名称
     * @return 应用列表
     */
    @Query("SELECT a FROM Application a WHERE a.name LIKE %:name% AND a.status = 1")
    List<Application> findByNameContaining(@Param("name") String name);
    
    /**
     * 根据名称模糊查询（分页）
     * 
     * @param name 应用名称
     * @param pageable 分页参数
     * @return 应用分页结果
     */
    @Query("SELECT a FROM Application a WHERE a.name LIKE %:name% AND a.status = 1")
    Page<Application> findByNameContaining(@Param("name") String name, Pageable pageable);
    
    /**
     * 统计各平台应用数量
     * 
     * @return 平台统计结果
     */
    @Query("SELECT a.platform, COUNT(a) FROM Application a WHERE a.status = 1 GROUP BY a.platform")
    List<Object[]> countByPlatform();
    
    /**
     * 统计各分类应用数量
     * 
     * @return 分类统计结果
     */
    @Query("SELECT a.category, COUNT(a) FROM Application a WHERE a.status = 1 GROUP BY a.category")
    List<Object[]> countByCategory();
} 