package com.applicationcenter.backend.repository;

import com.applicationcenter.backend.model.ErrorLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 错误日志Repository接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public interface ErrorLogRepository extends JpaRepository<ErrorLog, Long> {
    
    /**
     * 根据错误类型查询错误日志
     */
    List<ErrorLog> findByErrorTypeOrderByCreatedAtDesc(String errorType);
    
    /**
     * 根据严重程度查询错误日志
     */
    List<ErrorLog> findBySeverityOrderByCreatedAtDesc(ErrorLog.Severity severity);
    
    /**
     * 根据模块查询错误日志
     */
    List<ErrorLog> findByModuleOrderByCreatedAtDesc(String module);
    
    /**
     * 根据是否已解决查询错误日志
     */
    List<ErrorLog> findByResolvedOrderByCreatedAtDesc(Boolean resolved);
    
    /**
     * 根据用户ID查询错误日志
     */
    List<ErrorLog> findByUserIdOrderByCreatedAtDesc(Integer userId);
    
    /**
     * 根据时间范围查询错误日志
     */
    List<ErrorLog> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据严重程度和时间范围查询错误日志
     */
    List<ErrorLog> findBySeverityAndCreatedAtBetweenOrderByCreatedAtDesc(ErrorLog.Severity severity, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据模块和时间范围查询错误日志
     */
    List<ErrorLog> findByModuleAndCreatedAtBetweenOrderByCreatedAtDesc(String module, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 分页查询错误日志
     */
    Page<ErrorLog> findAllByOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * 根据严重程度分页查询错误日志
     */
    Page<ErrorLog> findBySeverityOrderByCreatedAtDesc(ErrorLog.Severity severity, Pageable pageable);
    
    /**
     * 根据是否已解决分页查询错误日志
     */
    Page<ErrorLog> findByResolvedOrderByCreatedAtDesc(Boolean resolved, Pageable pageable);
    
    /**
     * 根据时间范围分页查询错误日志
     */
    Page<ErrorLog> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 统计指定时间范围内的错误日志数量
     */
    @Query("SELECT COUNT(e) FROM ErrorLog e WHERE e.createdAt BETWEEN :startTime AND :endTime")
    Long countByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定严重程度的错误日志数量
     */
    @Query("SELECT COUNT(e) FROM ErrorLog e WHERE e.severity = :severity")
    Long countBySeverity(@Param("severity") ErrorLog.Severity severity);
    
    /**
     * 统计指定模块的错误日志数量
     */
    @Query("SELECT COUNT(e) FROM ErrorLog e WHERE e.module = :module")
    Long countByModule(@Param("module") String module);
    
    /**
     * 统计指定错误类型的错误日志数量
     */
    @Query("SELECT COUNT(e) FROM ErrorLog e WHERE e.errorType = :errorType")
    Long countByErrorType(@Param("errorType") String errorType);
    
    /**
     * 统计未解决的错误日志数量
     */
    @Query("SELECT COUNT(e) FROM ErrorLog e WHERE e.resolved = false")
    Long countUnresolvedErrors();
    
    /**
     * 获取最常见的错误类型
     */
    @Query("SELECT e.errorType, COUNT(e) as count FROM ErrorLog e GROUP BY e.errorType ORDER BY count DESC")
    List<Object[]> findMostCommonErrorTypes(Pageable pageable);
    
    /**
     * 获取最活跃的错误模块
     */
    @Query("SELECT e.module, COUNT(e) as count FROM ErrorLog e GROUP BY e.module ORDER BY count DESC")
    List<Object[]> findMostErrorProneModules(Pageable pageable);
    
    /**
     * 获取严重程度分布
     */
    @Query("SELECT e.severity, COUNT(e) as count FROM ErrorLog e GROUP BY e.severity ORDER BY count DESC")
    List<Object[]> findSeverityDistribution();
    
    /**
     * 获取最近的严重错误
     */
    @Query("SELECT e FROM ErrorLog e WHERE e.severity IN ('HIGH', 'CRITICAL') ORDER BY e.createdAt DESC")
    List<ErrorLog> findRecentCriticalErrors(Pageable pageable);
    
    /**
     * 删除指定时间之前的错误日志
     */
    @Modifying
    @Query("DELETE FROM ErrorLog e WHERE e.createdAt < :beforeTime")
    void deleteByCreatedAtBefore(@Param("beforeTime") LocalDateTime beforeTime);
    
    /**
     * 删除已解决的错误日志
     */
    @Modifying
    @Query("DELETE FROM ErrorLog e WHERE e.resolved = true")
    void deleteResolvedErrors();
} 