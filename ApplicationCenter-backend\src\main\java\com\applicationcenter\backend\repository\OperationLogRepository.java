package com.applicationcenter.backend.repository;

import com.applicationcenter.backend.model.OperationLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志Repository接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public interface OperationLogRepository extends JpaRepository<OperationLog, Long> {
    
    /**
     * 根据用户ID查询操作日志
     */
    List<OperationLog> findByUserIdOrderByCreatedAtDesc(Integer userId);
    
    /**
     * 根据用户名查询操作日志
     */
    List<OperationLog> findByUsernameOrderByCreatedAtDesc(String username);
    
    /**
     * 根据模块查询操作日志
     */
    List<OperationLog> findByModuleOrderByCreatedAtDesc(String module);
    
    /**
     * 根据操作类型查询操作日志
     */
    List<OperationLog> findByOperationOrderByCreatedAtDesc(String operation);
    
    /**
     * 根据日志级别查询操作日志
     */
    List<OperationLog> findByLogLevelOrderByCreatedAtDesc(OperationLog.LogLevel logLevel);
    
    /**
     * 根据时间范围查询操作日志
     */
    List<OperationLog> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据用户ID和时间范围查询操作日志
     */
    List<OperationLog> findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(Integer userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据模块和时间范围查询操作日志
     */
    List<OperationLog> findByModuleAndCreatedAtBetweenOrderByCreatedAtDesc(String module, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 分页查询操作日志
     */
    Page<OperationLog> findAllByOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * 根据用户ID分页查询操作日志
     */
    Page<OperationLog> findByUserIdOrderByCreatedAtDesc(Integer userId, Pageable pageable);
    
    /**
     * 根据模块分页查询操作日志
     */
    Page<OperationLog> findByModuleOrderByCreatedAtDesc(String module, Pageable pageable);
    
    /**
     * 根据时间范围分页查询操作日志
     */
    Page<OperationLog> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 统计指定时间范围内的操作日志数量
     */
    @Query("SELECT COUNT(o) FROM OperationLog o WHERE o.createdAt BETWEEN :startTime AND :endTime")
    Long countByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定用户的操作日志数量
     */
    @Query("SELECT COUNT(o) FROM OperationLog o WHERE o.userId = :userId")
    Long countByUserId(@Param("userId") Integer userId);
    
    /**
     * 统计指定模块的操作日志数量
     */
    @Query("SELECT COUNT(o) FROM OperationLog o WHERE o.module = :module")
    Long countByModule(@Param("module") String module);
    
    /**
     * 统计指定操作类型的操作日志数量
     */
    @Query("SELECT COUNT(o) FROM OperationLog o WHERE o.operation = :operation")
    Long countByOperation(@Param("operation") String operation);
    
    /**
     * 获取最活跃的用户（操作次数最多的用户）
     */
    @Query("SELECT o.username, COUNT(o) as count FROM OperationLog o GROUP BY o.username ORDER BY count DESC")
    List<Object[]> findMostActiveUsers(Pageable pageable);
    
    /**
     * 获取最常用的操作类型
     */
    @Query("SELECT o.operation, COUNT(o) as count FROM OperationLog o GROUP BY o.operation ORDER BY count DESC")
    List<Object[]> findMostCommonOperations(Pageable pageable);
    
    /**
     * 获取最活跃的模块
     */
    @Query("SELECT o.module, COUNT(o) as count FROM OperationLog o GROUP BY o.module ORDER BY count DESC")
    List<Object[]> findMostActiveModules(Pageable pageable);
    
    /**
     * 删除指定时间之前的日志
     */
    @Modifying
    @Query("DELETE FROM OperationLog o WHERE o.createdAt < :beforeTime")
    void deleteByCreatedAtBefore(@Param("beforeTime") LocalDateTime beforeTime);
} 