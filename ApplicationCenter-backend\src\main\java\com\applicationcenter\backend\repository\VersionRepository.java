package com.applicationcenter.backend.repository;

import com.applicationcenter.backend.model.Version;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 版本数据访问层
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public interface VersionRepository extends JpaRepository<Version, Integer> {
    
    /**
     * 根据应用ID查找版本列表
     * 
     * @param applicationId 应用ID
     * @return 版本列表
     */
    List<Version> findByApplicationIdOrderByCreatedAtDesc(Integer applicationId);
    
    /**
     * 根据应用ID分页查找版本列表
     * 
     * @param applicationId 应用ID
     * @param pageable 分页参数
     * @return 版本列表
     */
    Page<Version> findByApplicationIdOrderByCreatedAtDesc(Integer applicationId, Pageable pageable);
    
    /**
     * 根据应用ID和状态查找版本列表
     * 
     * @param applicationId 应用ID
     * @param status 状态
     * @return 版本列表
     */
    List<Version> findByApplicationIdAndStatusOrderByCreatedAtDesc(Integer applicationId, Version.Status status);
    
    /**
     * 根据应用ID查找最新发布的版本
     * 
     * @param applicationId 应用ID
     * @return 最新版本
     */
    Optional<Version> findFirstByApplicationIdAndStatusOrderByCreatedAtDesc(Integer applicationId, Version.Status status);
    
    /**
     * 根据应用ID和版本号查找版本
     * 
     * @param applicationId 应用ID
     * @param versionCode 版本号
     * @return 版本
     */
    Optional<Version> findByApplicationIdAndVersionCode(Integer applicationId, String versionCode);
    
    /**
     * 检查应用是否存在指定版本号
     * 
     * @param applicationId 应用ID
     * @param versionCode 版本号
     * @return 是否存在
     */
    boolean existsByApplicationIdAndVersionCode(Integer applicationId, String versionCode);
    
    /**
     * 根据状态查找版本列表
     * 
     * @param status 状态
     * @return 版本列表
     */
    List<Version> findByStatusOrderByCreatedAtDesc(Version.Status status);
    
    /**
     * 根据状态分页查找版本列表
     * 
     * @param status 状态
     * @param pageable 分页参数
     * @return 版本列表
     */
    Page<Version> findByStatusOrderByCreatedAtDesc(Version.Status status, Pageable pageable);
    
    /**
     * 根据创建者查找版本列表
     * 
     * @param createdBy 创建者ID
     * @return 版本列表
     */
    List<Version> findByCreatedByOrderByCreatedAtDesc(Integer createdBy);
    
    /**
     * 根据创建者分页查找版本列表
     * 
     * @param createdBy 创建者ID
     * @param pageable 分页参数
     * @return 版本列表
     */
    Page<Version> findByCreatedByOrderByCreatedAtDesc(Integer createdBy, Pageable pageable);
    
    /**
     * 搜索版本（根据版本名称或描述）
     * 
     * @param keyword 关键词
     * @return 版本列表
     */
    @Query("SELECT v FROM Version v WHERE v.versionName LIKE %:keyword% OR v.description LIKE %:keyword% ORDER BY v.createdAt DESC")
    List<Version> searchVersions(@Param("keyword") String keyword);
    
    /**
     * 搜索版本（分页）
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 版本列表
     */
    @Query("SELECT v FROM Version v WHERE v.versionName LIKE %:keyword% OR v.description LIKE %:keyword% ORDER BY v.createdAt DESC")
    Page<Version> searchVersions(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 获取版本统计信息
     * 
     * @return 统计信息
     */
    @Query("SELECT v.status, COUNT(v) FROM Version v GROUP BY v.status")
    List<Object[]> getVersionStatistics();
    
    /**
     * 根据应用ID获取版本统计信息
     * 
     * @param applicationId 应用ID
     * @return 统计信息
     */
    @Query("SELECT v.status, COUNT(v) FROM Version v WHERE v.application.id = :applicationId GROUP BY v.status")
    List<Object[]> getVersionStatisticsByApplication(@Param("applicationId") Integer applicationId);
    
    /**
     * 查找需要强制更新的版本
     * 
     * @param applicationId 应用ID
     * @return 强制更新版本列表
     */
    List<Version> findByApplicationIdAndIsForcedUpdateTrueOrderByCreatedAtDesc(Integer applicationId);
    
    /**
     * 根据文件哈希查找版本
     * 
     * @param fileHash 文件哈希
     * @return 版本
     */
    Optional<Version> findByFileHash(String fileHash);
    
    /**
     * 检查文件哈希是否存在
     * 
     * @param fileHash 文件哈希
     * @return 是否存在
     */
    boolean existsByFileHash(String fileHash);
} 