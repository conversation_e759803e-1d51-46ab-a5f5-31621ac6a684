package com.applicationcenter.backend.service;

import com.applicationcenter.backend.model.AdminUser;
import com.applicationcenter.backend.util.ApiResponse;

/**
 * 管理员服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AdminUserService {
    
    /**
     * 管理员登录
     * 
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    ApiResponse<Object> login(String username, String password);
    
    /**
     * 根据用户名查找管理员
     * 
     * @param username 用户名
     * @return 管理员信息
     */
    AdminUser findByUsername(String username);
    
    /**
     * 根据ID查找管理员
     * 
     * @param id 管理员ID
     * @return 管理员信息
     */
    AdminUser findById(Integer id);
    
    /**
     * 创建管理员
     * 
     * @param adminUser 管理员信息
     * @return 创建结果
     */
    ApiResponse<Object> createAdmin(AdminUser adminUser);
    
    /**
     * 更新管理员信息
     * 
     * @param adminUser 管理员信息
     * @return 更新结果
     */
    ApiResponse<Object> updateAdmin(AdminUser adminUser);
    
    /**
     * 删除管理员
     * 
     * @param id 管理员ID
     * @return 删除结果
     */
    ApiResponse<Object> deleteAdmin(Integer id);
} 