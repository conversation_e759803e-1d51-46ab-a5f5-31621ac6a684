package com.applicationcenter.backend.service;

import com.applicationcenter.backend.model.Application;
import com.applicationcenter.backend.util.ApiResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 应用服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface ApplicationService {
    
    /**
     * 创建应用
     * 
     * @param application 应用信息
     * @param adminId 创建者ID
     * @return 创建结果
     */
    ApiResponse<Object> createApplication(Application application, Integer adminId);
    
    /**
     * 更新应用
     * 
     * @param id 应用ID
     * @param application 应用信息
     * @return 更新结果
     */
    ApiResponse<Object> updateApplication(Integer id, Application application);
    
    /**
     * 删除应用
     * 
     * @param id 应用ID
     * @return 删除结果
     */
    ApiResponse<Object> deleteApplication(Integer id);
    
    /**
     * 获取应用详情
     * 
     * @param id 应用ID
     * @return 应用详情
     */
    ApiResponse<Object> getApplication(Integer id);
    
    /**
     * 获取应用列表（分页）
     * 
     * @param pageable 分页参数
     * @return 应用列表
     */
    ApiResponse<Object> getApplicationList(Pageable pageable);
    
    /**
     * 获取应用列表（不分页）
     * 
     * @return 应用列表
     */
    ApiResponse<Object> getAllApplications();
    
    /**
     * 根据平台获取应用列表
     * 
     * @param platform 平台
     * @return 应用列表
     */
    ApiResponse<Object> getApplicationsByPlatform(Application.Platform platform);
    
    /**
     * 根据分类获取应用列表
     * 
     * @param category 分类
     * @return 应用列表
     */
    ApiResponse<Object> getApplicationsByCategory(String category);
    
    /**
     * 根据开发者获取应用列表
     * 
     * @param developer 开发者
     * @return 应用列表
     */
    ApiResponse<Object> getApplicationsByDeveloper(String developer);
    
    /**
     * 搜索应用
     * 
     * @param keyword 关键词
     * @return 应用列表
     */
    ApiResponse<Object> searchApplications(String keyword);
    
    /**
     * 启用/禁用应用
     * 
     * @param id 应用ID
     * @param status 状态（1:启用, 0:禁用）
     * @return 操作结果
     */
    ApiResponse<Object> updateApplicationStatus(Integer id, Integer status);
    
    /**
     * 获取应用统计信息
     * 
     * @return 统计信息
     */
    ApiResponse<Object> getApplicationStatistics();
    
    /**
     * 检查包名是否存在
     * 
     * @param packageName 包名
     * @return 是否存在
     */
    boolean existsByPackageName(String packageName);
    
    /**
     * 根据ID查找应用
     * 
     * @param id 应用ID
     * @return 应用
     */
    Application findById(Integer id);
    
    /**
     * 根据包名查找应用
     * 
     * @param packageName 包名
     * @return 应用
     */
    Application findByPackageName(String packageName);
    
    /**
     * 获取公开的应用列表（无需认证）
     * 
     * @return 公开的应用列表
     */
    ApiResponse<Object> getPublicApplications();
} 