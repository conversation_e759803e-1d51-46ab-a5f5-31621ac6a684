package com.applicationcenter.backend.service;

import com.applicationcenter.backend.model.AdminUser;
import com.applicationcenter.backend.repository.AdminUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 自定义UserDetailsService实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class CustomUserDetailsService implements UserDetailsService {
    
    @Autowired
    private AdminUserRepository adminUserRepository;
    
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        try {
            // 查找管理员用户
            AdminUser adminUser = adminUserRepository.findByUsernameAndStatus(username, 1)
                    .orElseThrow(() -> new UsernameNotFoundException("用户不存在或已被禁用: " + username));
            
            // 创建权限列表
            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            
            // 根据用户状态添加额外权限
            if (adminUser.getStatus() == 1) {
                authorities.add(new SimpleGrantedAuthority("ROLE_ACTIVE"));
            }
            
            // 创建UserDetails对象
            return new User(
                    adminUser.getUsername(),
                    adminUser.getPassword(),
                    authorities
            );
        } catch (Exception e) {
            throw new UsernameNotFoundException("加载用户信息失败: " + e.getMessage());
        }
    }
} 