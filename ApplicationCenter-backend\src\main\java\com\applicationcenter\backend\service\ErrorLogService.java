package com.applicationcenter.backend.service;

import com.applicationcenter.backend.model.ErrorLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 错误日志Service接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface ErrorLogService {
    /**
     * 记录错误日志
     */
    ErrorLog save(ErrorLog log);

    /**
     * 分页查询错误日志
     */
    Page<ErrorLog> getLogs(Pageable pageable);

    /**
     * 根据错误类型分页查询错误日志
     */
    Page<ErrorLog> getLogsByErrorType(String errorType, Pageable pageable);

    /**
     * 根据模块分页查询错误日志
     */
    Page<ErrorLog> getLogsByModule(String module, Pageable pageable);

    /**
     * 根据严重程度分页查询错误日志
     */
    Page<ErrorLog> getLogsBySeverity(String severity, Pageable pageable);

    /**
     * 根据时间范围分页查询错误日志
     */
    Page<ErrorLog> getLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 根据是否已解决分页查询错误日志
     */
    Page<ErrorLog> getLogsByResolved(boolean resolved, Pageable pageable);

    /**
     * 标记错误为已解决
     */
    ErrorLog markAsResolved(Long logId, Integer resolvedByUserId);

    /**
     * 删除指定时间之前的日志
     */
    void deleteLogsBefore(LocalDateTime beforeTime);

    /**
     * 删除已解决的错误日志
     */
    void deleteResolvedLogs();

    /**
     * 获取最常见的错误类型
     */
    List<Object[]> getMostCommonErrorTypes(Pageable pageable);

    /**
     * 获取最容易出错的模块
     */
    List<Object[]> getMostErrorProneModules(Pageable pageable);

    /**
     * 获取严重程度分布
     */
    List<Object[]> getSeverityDistribution();

    /**
     * 获取最近的严重错误
     */
    List<ErrorLog> getRecentCriticalErrors(Pageable pageable);
} 