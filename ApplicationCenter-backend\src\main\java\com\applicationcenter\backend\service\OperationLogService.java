package com.applicationcenter.backend.service;

import com.applicationcenter.backend.model.OperationLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志Service接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface OperationLogService {
    /**
     * 记录操作日志
     */
    OperationLog save(OperationLog log);

    /**
     * 分页查询操作日志
     */
    Page<OperationLog> getLogs(Pageable pageable);

    /**
     * 根据用户ID分页查询操作日志
     */
    Page<OperationLog> getLogsByUserId(Integer userId, Pageable pageable);

    /**
     * 根据模块分页查询操作日志
     */
    Page<OperationLog> getLogsByModule(String module, Pageable pageable);

    /**
     * 根据时间范围分页查询操作日志
     */
    Page<OperationLog> getLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 删除指定时间之前的日志
     */
    void deleteLogsBefore(LocalDateTime beforeTime);

    /**
     * 获取最活跃的用户
     */
    List<Object[]> getMostActiveUsers(Pageable pageable);

    /**
     * 获取最常用的操作类型
     */
    List<Object[]> getMostCommonOperations(Pageable pageable);

    /**
     * 获取最活跃的模块
     */
    List<Object[]> getMostActiveModules(Pageable pageable);
} 