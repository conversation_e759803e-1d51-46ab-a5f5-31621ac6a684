package com.applicationcenter.backend.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 统计Service接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface StatisticsService {
    
    /**
     * 获取系统概览统计
     */
    Map<String, Object> getSystemOverview();
    
    /**
     * 获取应用统计信息
     */
    Map<String, Object> getApplicationStatistics();
    
    /**
     * 获取版本统计信息
     */
    Map<String, Object> getVersionStatistics();
    
    /**
     * 获取用户活跃度统计
     */
    Map<String, Object> getUserActivityStatistics(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取操作日志统计
     */
    Map<String, Object> getOperationLogStatistics(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取错误日志统计
     */
    Map<String, Object> getErrorLogStatistics(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取文件上传统计
     */
    Map<String, Object> getFileUploadStatistics(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取平台分布统计
     */
    List<Map<String, Object>> getPlatformDistribution();
    
    /**
     * 获取版本状态分布统计
     */
    List<Map<String, Object>> getVersionStatusDistribution();
    
    /**
     * 获取错误严重程度分布统计
     */
    List<Map<String, Object>> getErrorSeverityDistribution();
    
    /**
     * 获取每日活跃用户统计
     */
    List<Map<String, Object>> getDailyActiveUsers(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取每日操作次数统计
     */
    List<Map<String, Object>> getDailyOperations(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取每日错误次数统计
     */
    List<Map<String, Object>> getDailyErrors(LocalDateTime startTime, LocalDateTime endTime);
} 