package com.applicationcenter.backend.service;

import com.applicationcenter.backend.model.Version;
import com.applicationcenter.backend.util.ApiResponse;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 版本服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface VersionService {
    
    /**
     * 创建版本
     * 
     * @param version 版本信息
     * @param adminId 创建者ID
     * @return 创建结果
     */
    ApiResponse<Object> createVersion(Version version, Integer adminId);
    
    /**
     * 上传版本文件
     * 
     * @param versionId 版本ID
     * @param file 文件
     * @param adminId 操作者ID
     * @return 上传结果
     */
    ApiResponse<Object> uploadVersionFile(Integer versionId, MultipartFile file, Integer adminId);
    
    /**
     * 更新版本信息
     * 
     * @param id 版本ID
     * @param version 版本信息
     * @return 更新结果
     */
    ApiResponse<Object> updateVersion(Integer id, Version version);
    
    /**
     * 删除版本
     * 
     * @param id 版本ID
     * @return 删除结果
     */
    ApiResponse<Object> deleteVersion(Integer id);
    
    /**
     * 获取版本详情
     * 
     * @param id 版本ID
     * @return 版本详情
     */
    ApiResponse<Object> getVersion(Integer id);
    
    /**
     * 获取应用的所有版本列表
     * 
     * @param applicationId 应用ID
     * @return 版本列表
     */
    ApiResponse<Object> getVersionsByApplication(Integer applicationId);
    
    /**
     * 获取应用的所有版本列表（分页）
     * 
     * @param applicationId 应用ID
     * @param pageable 分页参数
     * @return 版本列表
     */
    ApiResponse<Object> getVersionsByApplication(Integer applicationId, Pageable pageable);
    
    /**
     * 获取应用的最新发布版本
     * 
     * @param applicationId 应用ID
     * @return 最新版本
     */
    ApiResponse<Object> getLatestPublishedVersion(Integer applicationId);
    
    /**
     * 发布版本
     * 
     * @param id 版本ID
     * @return 发布结果
     */
    ApiResponse<Object> publishVersion(Integer id);
    
    /**
     * 下架版本
     * 
     * @param id 版本ID
     * @return 下架结果
     */
    ApiResponse<Object> deprecateVersion(Integer id);
    
    /**
     * 更新版本状态
     * 
     * @param id 版本ID
     * @param status 状态
     * @return 更新结果
     */
    ApiResponse<Object> updateVersionStatus(Integer id, Version.Status status);
    
    /**
     * 设置强制更新
     * 
     * @param id 版本ID
     * @param isForcedUpdate 是否强制更新
     * @return 设置结果
     */
    ApiResponse<Object> setForcedUpdate(Integer id, Boolean isForcedUpdate);
    
    /**
     * 搜索版本
     * 
     * @param keyword 关键词
     * @return 版本列表
     */
    ApiResponse<Object> searchVersions(String keyword);
    
    /**
     * 搜索版本（分页）
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 版本列表
     */
    ApiResponse<Object> searchVersions(String keyword, Pageable pageable);
    
    /**
     * 根据状态获取版本列表
     * 
     * @param status 状态
     * @return 版本列表
     */
    ApiResponse<Object> getVersionsByStatus(Version.Status status);
    
    /**
     * 根据状态获取版本列表（分页）
     * 
     * @param status 状态
     * @param pageable 分页参数
     * @return 版本列表
     */
    ApiResponse<Object> getVersionsByStatus(Version.Status status, Pageable pageable);
    
    /**
     * 获取版本统计信息
     * 
     * @return 统计信息
     */
    ApiResponse<Object> getVersionStatistics();
    
    /**
     * 获取应用的版本统计信息
     * 
     * @param applicationId 应用ID
     * @return 统计信息
     */
    ApiResponse<Object> getVersionStatisticsByApplication(Integer applicationId);
    
    /**
     * 检查版本号是否存在
     * 
     * @param applicationId 应用ID
     * @param versionCode 版本号
     * @return 是否存在
     */
    boolean existsByVersionCode(Integer applicationId, String versionCode);
    
    /**
     * 根据ID查找版本
     * 
     * @param id 版本ID
     * @return 版本
     */
    Version findById(Integer id);
    
    /**
     * 根据应用ID和版本号查找版本
     * 
     * @param applicationId 应用ID
     * @param versionCode 版本号
     * @return 版本
     */
    Version findByApplicationIdAndVersionCode(Integer applicationId, String versionCode);
    
    /**
     * 获取版本下载链接
     * 
     * @param id 版本ID
     * @return 下载链接
     */
    ApiResponse<Object> getDownloadUrl(Integer id);
    
    /**
     * 检查更新
     * 
     * @param applicationId 应用ID
     * @param currentVersion 当前版本号
     * @return 更新信息
     */
    ApiResponse<Object> checkForUpdate(Integer applicationId, String currentVersion);
    
    /**
     * 获取应用的强制更新版本
     * 
     * @param applicationId 应用ID
     * @return 强制更新版本
     */
    ApiResponse<Object> getForcedUpdateVersion(Integer applicationId);
} 