package com.applicationcenter.backend.service.impl;

import com.applicationcenter.backend.model.AdminUser;
import com.applicationcenter.backend.repository.AdminUserRepository;
import com.applicationcenter.backend.service.AdminUserService;
import com.applicationcenter.backend.util.ApiResponse;
import com.applicationcenter.backend.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 管理员服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class AdminUserServiceImpl implements AdminUserService {
    
    @Autowired
    private AdminUserRepository adminUserRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Override
    public ApiResponse<Object> login(String username, String password) {
        try {
            // 查找管理员
            Optional<AdminUser> adminOptional = adminUserRepository.findByUsernameAndStatus(username, 1);
            if (!adminOptional.isPresent()) {
                return ApiResponse.error("用户名或密码错误");
            }
            
            AdminUser admin = adminOptional.get();
            
            // 验证密码
            if (!passwordEncoder.matches(password, admin.getPassword())) {
                return ApiResponse.error("用户名或密码错误");
            }
            
            // 更新最后登录时间
            admin.setLastLogin(LocalDateTime.now());
            adminUserRepository.save(admin);
            
            // 生成JWT token
            String token = jwtUtil.generateToken(admin.getUsername(), admin.getId());
            
            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            
            Map<String, Object> user = new HashMap<>();
            user.put("id", admin.getId());
            user.put("username", admin.getUsername());
            data.put("user", user);
            
            return ApiResponse.success("登录成功", data);
            
        } catch (Exception e) {
            return ApiResponse.error("登录失败：" + e.getMessage());
        }
    }
    
    @Override
    public AdminUser findByUsername(String username) {
        return adminUserRepository.findByUsername(username).orElse(null);
    }
    
    @Override
    public AdminUser findById(Integer id) {
        return adminUserRepository.findById(id).orElse(null);
    }
    
    @Override
    public ApiResponse<Object> createAdmin(AdminUser adminUser) {
        try {
            // 检查用户名是否已存在
            if (adminUserRepository.existsByUsername(adminUser.getUsername())) {
                return ApiResponse.error("用户名已存在");
            }
            
            // 加密密码
            adminUser.setPassword(passwordEncoder.encode(adminUser.getPassword()));
            adminUser.setStatus(1);
            adminUser.setCreatedAt(LocalDateTime.now());
            adminUser.setUpdatedAt(LocalDateTime.now());
            
            AdminUser savedAdmin = adminUserRepository.save(adminUser);
            
            return ApiResponse.success("管理员创建成功", savedAdmin);
            
        } catch (Exception e) {
            return ApiResponse.error("创建管理员失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> updateAdmin(AdminUser adminUser) {
        try {
            Optional<AdminUser> existingAdmin = adminUserRepository.findById(adminUser.getId());
            if (!existingAdmin.isPresent()) {
                return ApiResponse.error("管理员不存在");
            }
            
            AdminUser existing = existingAdmin.get();
            
            // 如果密码不为空，则更新密码
            if (adminUser.getPassword() != null && !adminUser.getPassword().isEmpty()) {
                existing.setPassword(passwordEncoder.encode(adminUser.getPassword()));
            }
            
            existing.setStatus(adminUser.getStatus());
            existing.setUpdatedAt(LocalDateTime.now());
            
            AdminUser updatedAdmin = adminUserRepository.save(existing);
            
            return ApiResponse.success("管理员更新成功", updatedAdmin);
            
        } catch (Exception e) {
            return ApiResponse.error("更新管理员失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> deleteAdmin(Integer id) {
        try {
            if (!adminUserRepository.existsById(id)) {
                return ApiResponse.error("管理员不存在");
            }
            
            adminUserRepository.deleteById(id);
            
            return ApiResponse.success("管理员删除成功");
            
        } catch (Exception e) {
            return ApiResponse.error("删除管理员失败：" + e.getMessage());
        }
    }
} 