package com.applicationcenter.backend.service.impl;

import com.applicationcenter.backend.model.Application;
import com.applicationcenter.backend.repository.ApplicationRepository;
import com.applicationcenter.backend.service.ApplicationService;
import com.applicationcenter.backend.util.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 应用服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class ApplicationServiceImpl implements ApplicationService {
    
    @Autowired
    private ApplicationRepository applicationRepository;
    
    @Override
    public ApiResponse<Object> createApplication(Application application, Integer adminId) {
        try {
            // 检查包名是否已存在
            if (applicationRepository.existsByPackageName(application.getPackageName())) {
                return ApiResponse.error("包名已存在");
            }
            
            // 设置创建者ID和时间
            application.setCreatedBy(adminId);
            application.setCreatedAt(LocalDateTime.now());
            application.setUpdatedAt(LocalDateTime.now());
            
            // 设置默认状态为启用
            if (application.getStatus() == null) {
                application.setStatus(1);
            }
            
            Application savedApplication = applicationRepository.save(application);
            
            return ApiResponse.success("应用创建成功", savedApplication);
            
        } catch (Exception e) {
            return ApiResponse.error("创建应用失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> updateApplication(Integer id, Application application) {
        try {
            Application existingApplication = applicationRepository.findById(id).orElse(null);
            if (existingApplication == null) {
                return ApiResponse.error("应用不存在");
            }
            
            // 检查包名是否被其他应用使用
            if (!existingApplication.getPackageName().equals(application.getPackageName()) &&
                applicationRepository.existsByPackageName(application.getPackageName())) {
                return ApiResponse.error("包名已存在");
            }
            
            // 更新应用信息
            existingApplication.setName(application.getName());
            existingApplication.setPackageName(application.getPackageName());
            existingApplication.setPlatform(application.getPlatform());
            existingApplication.setDescription(application.getDescription());
            existingApplication.setIcon(application.getIcon());
            existingApplication.setCategory(application.getCategory());
            existingApplication.setDeveloper(application.getDeveloper());
            existingApplication.setWebsite(application.getWebsite());
            existingApplication.setStatus(application.getStatus());
            existingApplication.setUpdatedAt(LocalDateTime.now());
            
            Application updatedApplication = applicationRepository.save(existingApplication);
            
            return ApiResponse.success("应用更新成功", updatedApplication);
            
        } catch (Exception e) {
            return ApiResponse.error("更新应用失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> deleteApplication(Integer id) {
        try {
            if (!applicationRepository.existsById(id)) {
                return ApiResponse.error("应用不存在");
            }
            
            applicationRepository.deleteById(id);
            
            return ApiResponse.success("应用删除成功");
            
        } catch (Exception e) {
            return ApiResponse.error("删除应用失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getApplication(Integer id) {
        try {
            Application application = applicationRepository.findById(id).orElse(null);
            if (application == null) {
                return ApiResponse.error("应用不存在");
            }
            
            return ApiResponse.success("获取应用成功", application);
            
        } catch (Exception e) {
            return ApiResponse.error("获取应用失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getApplicationList(Pageable pageable) {
        try {
            Page<Application> applications = applicationRepository.findByStatus(1, pageable);
            
            Map<String, Object> data = new HashMap<>();
            data.put("applications", applications.getContent());
            data.put("totalElements", applications.getTotalElements());
            data.put("totalPages", applications.getTotalPages());
            data.put("currentPage", applications.getNumber());
            data.put("pageSize", applications.getSize());
            
            return ApiResponse.success("获取应用列表成功", data);
            
        } catch (Exception e) {
            return ApiResponse.error("获取应用列表失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getAllApplications() {
        try {
            List<Application> applications = applicationRepository.findByStatus(1);
            return ApiResponse.success("获取应用列表成功", applications);
            
        } catch (Exception e) {
            return ApiResponse.error("获取应用列表失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getApplicationsByPlatform(Application.Platform platform) {
        try {
            List<Application> applications = applicationRepository.findByPlatformAndStatus(platform, 1);
            return ApiResponse.success("获取应用列表成功", applications);
            
        } catch (Exception e) {
            return ApiResponse.error("获取应用列表失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getApplicationsByCategory(String category) {
        try {
            List<Application> applications = applicationRepository.findByCategory(category);
            return ApiResponse.success("获取应用列表成功", applications);
            
        } catch (Exception e) {
            return ApiResponse.error("获取应用列表失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getApplicationsByDeveloper(String developer) {
        try {
            List<Application> applications = applicationRepository.findByDeveloper(developer);
            return ApiResponse.success("获取应用列表成功", applications);
            
        } catch (Exception e) {
            return ApiResponse.error("获取应用列表失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> searchApplications(String keyword) {
        try {
            List<Application> applications = applicationRepository.findByNameContaining(keyword);
            return ApiResponse.success("搜索应用成功", applications);
            
        } catch (Exception e) {
            return ApiResponse.error("搜索应用失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> updateApplicationStatus(Integer id, Integer status) {
        try {
            Application application = applicationRepository.findById(id).orElse(null);
            if (application == null) {
                return ApiResponse.error("应用不存在");
            }
            
            application.setStatus(status);
            application.setUpdatedAt(LocalDateTime.now());
            
            Application updatedApplication = applicationRepository.save(application);
            
            String message = status == 1 ? "应用启用成功" : "应用禁用成功";
            return ApiResponse.success(message, updatedApplication);
            
        } catch (Exception e) {
            return ApiResponse.error("更新应用状态失败：" + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getApplicationStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 总应用数
            long totalApplications = applicationRepository.count();
            statistics.put("totalApplications", totalApplications);
            
            // 启用应用数
            long enabledApplications = applicationRepository.findByStatus(1).size();
            statistics.put("enabledApplications", enabledApplications);
            
            // 禁用应用数
            long disabledApplications = applicationRepository.findByStatus(0).size();
            statistics.put("disabledApplications", disabledApplications);
            
            // 各平台应用数量
            List<Object[]> platformStats = applicationRepository.countByPlatform();
            Map<String, Long> platformCounts = new HashMap<>();
            for (Object[] stat : platformStats) {
                platformCounts.put(stat[0].toString(), (Long) stat[1]);
            }
            statistics.put("platformStatistics", platformCounts);
            
            // 各分类应用数量
            List<Object[]> categoryStats = applicationRepository.countByCategory();
            Map<String, Long> categoryCounts = new HashMap<>();
            for (Object[] stat : categoryStats) {
                categoryCounts.put(stat[0].toString(), (Long) stat[1]);
            }
            statistics.put("categoryStatistics", categoryCounts);
            
            return ApiResponse.success("获取统计信息成功", statistics);
            
        } catch (Exception e) {
            return ApiResponse.error("获取统计信息失败：" + e.getMessage());
        }
    }
    
    @Override
    public boolean existsByPackageName(String packageName) {
        return applicationRepository.existsByPackageName(packageName);
    }
    
    @Override
    public Application findById(Integer id) {
        return applicationRepository.findById(id).orElse(null);
    }
    
    @Override
    public Application findByPackageName(String packageName) {
        return applicationRepository.findByPackageName(packageName).orElse(null);
    }
    
    @Override
    public ApiResponse<Object> getPublicApplications() {
        try {
            // 获取所有启用的应用，只返回公开信息
            List<Application> applications = applicationRepository.findByStatus(1);
            
            // 构建公开的应用信息（不包含敏感数据）
            List<Map<String, Object>> publicApps = applications.stream()
                .map(app -> {
                    Map<String, Object> publicApp = new HashMap<>();
                    publicApp.put("id", app.getId());
                    publicApp.put("name", app.getName());
                    publicApp.put("packageName", app.getPackageName());
                    publicApp.put("platform", app.getPlatform());
                    publicApp.put("description", app.getDescription());
                    publicApp.put("icon", app.getIcon());
                    publicApp.put("category", app.getCategory());
                    publicApp.put("developer", app.getDeveloper());
                    publicApp.put("website", app.getWebsite());
                    return publicApp;
                })
                .toList();
            
            return ApiResponse.success("获取公开应用列表成功", publicApps);
            
        } catch (Exception e) {
            return ApiResponse.error("获取公开应用列表失败：" + e.getMessage());
        }
    }
} 