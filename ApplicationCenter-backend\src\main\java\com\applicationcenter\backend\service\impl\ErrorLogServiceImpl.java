package com.applicationcenter.backend.service.impl;

import com.applicationcenter.backend.model.ErrorLog;
import com.applicationcenter.backend.repository.ErrorLogRepository;
import com.applicationcenter.backend.service.ErrorLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class ErrorLogServiceImpl implements ErrorLogService {

    @Autowired
    private ErrorLogRepository errorLogRepository;

    @Override
    public ErrorLog save(ErrorLog log) {
        return errorLogRepository.save(log);
    }

    @Override
    public Page<ErrorLog> getLogs(Pageable pageable) {
        return errorLogRepository.findAllByOrderByCreatedAtDesc(pageable);
    }

    @Override
    public Page<ErrorLog> getLogsByErrorType(String errorType, Pageable pageable) {
        // 由于Repository中没有分页方法，先获取所有结果再手动分页
        List<ErrorLog> allLogs = errorLogRepository.findByErrorTypeOrderByCreatedAtDesc(errorType);
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), allLogs.size());
        return new PageImpl<>(
            allLogs.subList(start, end), pageable, allLogs.size());
    }

    @Override
    public Page<ErrorLog> getLogsByModule(String module, Pageable pageable) {
        // 由于Repository中没有分页方法，先获取所有结果再手动分页
        List<ErrorLog> allLogs = errorLogRepository.findByModuleOrderByCreatedAtDesc(module);
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), allLogs.size());
        return new PageImpl<>(
            allLogs.subList(start, end), pageable, allLogs.size());
    }

    @Override
    public Page<ErrorLog> getLogsBySeverity(String severity, Pageable pageable) {
        // 将字符串转换为枚举类型
        ErrorLog.Severity severityEnum = ErrorLog.Severity.valueOf(severity.toUpperCase());
        return errorLogRepository.findBySeverityOrderByCreatedAtDesc(severityEnum, pageable);
    }

    @Override
    public Page<ErrorLog> getLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        return errorLogRepository.findByCreatedAtBetweenOrderByCreatedAtDesc(startTime, endTime, pageable);
    }

    @Override
    public Page<ErrorLog> getLogsByResolved(boolean resolved, Pageable pageable) {
        return errorLogRepository.findByResolvedOrderByCreatedAtDesc(resolved, pageable);
    }

    @Override
    public ErrorLog markAsResolved(Long logId, Integer resolvedByUserId) {
        ErrorLog log = errorLogRepository.findById(logId).orElse(null);
        if (log != null) {
            log.setResolved(true);
            log.setResolvedAt(LocalDateTime.now());
            log.setResolvedBy(resolvedByUserId);
            return errorLogRepository.save(log);
        }
        return null;
    }

    @Override
    @Transactional
    public void deleteLogsBefore(LocalDateTime beforeTime) {
        errorLogRepository.deleteByCreatedAtBefore(beforeTime);
    }

    @Override
    @Transactional
    public void deleteResolvedLogs() {
        errorLogRepository.deleteResolvedErrors();
    }

    @Override
    public List<Object[]> getMostCommonErrorTypes(Pageable pageable) {
        return errorLogRepository.findMostCommonErrorTypes(pageable);
    }

    @Override
    public List<Object[]> getMostErrorProneModules(Pageable pageable) {
        return errorLogRepository.findMostErrorProneModules(pageable);
    }

    @Override
    public List<Object[]> getSeverityDistribution() {
        return errorLogRepository.findSeverityDistribution();
    }

    @Override
    public List<ErrorLog> getRecentCriticalErrors(Pageable pageable) {
        return errorLogRepository.findRecentCriticalErrors(pageable);
    }
} 