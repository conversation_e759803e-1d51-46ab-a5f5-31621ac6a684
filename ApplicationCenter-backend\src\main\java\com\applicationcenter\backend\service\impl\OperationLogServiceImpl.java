package com.applicationcenter.backend.service.impl;

import com.applicationcenter.backend.model.OperationLog;
import com.applicationcenter.backend.repository.OperationLogRepository;
import com.applicationcenter.backend.service.OperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Autowired
    private OperationLogRepository operationLogRepository;

    @Override
    public OperationLog save(OperationLog log) {
        System.out.println("[Service调试] 准备保存操作日志到数据库:");
        System.out.println("[Service调试] - 用户ID: " + log.getUserId());
        System.out.println("[Service调试] - 用户名: " + log.getUsername());
        System.out.println("[Service调试] - 操作: " + log.getOperation());
        System.out.println("[Service调试] - 模块: " + log.getModule());
        
        try {
            OperationLog savedLog = operationLogRepository.save(log);
            System.out.println("[Service调试] 数据库保存成功，ID: " + savedLog.getId());
            return savedLog;
        } catch (Exception e) {
            System.err.println("[Service调试] 数据库保存失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    public Page<OperationLog> getLogs(Pageable pageable) {
        return operationLogRepository.findAllByOrderByCreatedAtDesc(pageable);
    }

    @Override
    public Page<OperationLog> getLogsByUserId(Integer userId, Pageable pageable) {
        return operationLogRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    @Override
    public Page<OperationLog> getLogsByModule(String module, Pageable pageable) {
        return operationLogRepository.findByModuleOrderByCreatedAtDesc(module, pageable);
    }

    @Override
    public Page<OperationLog> getLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        return operationLogRepository.findByCreatedAtBetweenOrderByCreatedAtDesc(startTime, endTime, pageable);
    }

    @Override
    @Transactional
    public void deleteLogsBefore(LocalDateTime beforeTime) {
        operationLogRepository.deleteByCreatedAtBefore(beforeTime);
    }

    @Override
    public List<Object[]> getMostActiveUsers(Pageable pageable) {
        return operationLogRepository.findMostActiveUsers(pageable);
    }

    @Override
    public List<Object[]> getMostCommonOperations(Pageable pageable) {
        return operationLogRepository.findMostCommonOperations(pageable);
    }

    @Override
    public List<Object[]> getMostActiveModules(Pageable pageable) {
        return operationLogRepository.findMostActiveModules(pageable);
    }
} 