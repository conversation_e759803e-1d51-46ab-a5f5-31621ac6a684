package com.applicationcenter.backend.service.impl;

import com.applicationcenter.backend.model.Application;
import com.applicationcenter.backend.model.Version;
import com.applicationcenter.backend.repository.ApplicationRepository;
import com.applicationcenter.backend.repository.ErrorLogRepository;
import com.applicationcenter.backend.repository.OperationLogRepository;
import com.applicationcenter.backend.repository.VersionRepository;
import com.applicationcenter.backend.service.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private VersionRepository versionRepository;

    @Autowired
    private OperationLogRepository operationLogRepository;

    @Autowired
    private ErrorLogRepository errorLogRepository;

    @Override
    public Map<String, Object> getSystemOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        // 应用总数
        long totalApplications = applicationRepository.count();
        overview.put("totalApplications", totalApplications);
        
        // 版本总数
        long totalVersions = versionRepository.count();
        overview.put("totalVersions", totalVersions);
        
        // 今日操作次数
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        long todayOperations = operationLogRepository.countByCreatedAtBetween(today, LocalDateTime.now());
        overview.put("todayOperations", todayOperations);
        
        // 今日错误次数
        long todayErrors = errorLogRepository.countByCreatedAtBetween(today, LocalDateTime.now());
        overview.put("todayErrors", todayErrors);
        
        // 未解决错误数
        long unresolvedErrors = errorLogRepository.countUnresolvedErrors();
        overview.put("unresolvedErrors", unresolvedErrors);
        
        return overview;
    }

    @Override
    public Map<String, Object> getApplicationStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 应用总数
        long totalApplications = applicationRepository.count();
        stats.put("totalApplications", totalApplications);
        
        // 各平台应用数量（暂时注释，等待Repository方法实现）
        // List<Object[]> platformStats = applicationRepository.findPlatformDistribution();
        Map<String, Long> platformDistribution = new HashMap<>();
        // for (Object[] result : platformStats) {
        //     String platform = (String) result[0];
        //     Long count = (Long) result[1];
        //     platformDistribution.put(platform, count);
        // }
        stats.put("platformDistribution", platformDistribution);
        
        return stats;
    }

    @Override
    public Map<String, Object> getVersionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 版本总数
        long totalVersions = versionRepository.count();
        stats.put("totalVersions", totalVersions);
        
        // 各状态版本数量（暂时注释，等待Repository方法实现）
        // List<Object[]> statusStats = versionRepository.findStatusDistribution();
        Map<String, Long> statusDistribution = new HashMap<>();
        // for (Object[] result : statusStats) {
        //     String status = (String) result[0];
        //     Long count = (Long) result[1];
        //     statusDistribution.put(status, count);
        // }
        stats.put("statusDistribution", statusDistribution);
        
        // 最新版本信息（暂时注释，等待Repository方法实现）
        // List<Version> latestVersions = versionRepository.findLatestVersions();
        stats.put("latestVersions", new ArrayList<>());
        
        return stats;
    }

    @Override
    public Map<String, Object> getUserActivityStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 活跃用户数
        List<Object[]> activeUsers = operationLogRepository.findMostActiveUsers(
            org.springframework.data.domain.PageRequest.of(0, 10));
        stats.put("activeUsers", activeUsers);
        
        // 用户操作次数
        long totalOperations = operationLogRepository.countByCreatedAtBetween(startTime, endTime);
        stats.put("totalOperations", totalOperations);
        
        return stats;
    }

    @Override
    public Map<String, Object> getOperationLogStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 操作总数
        long totalOperations = operationLogRepository.countByCreatedAtBetween(startTime, endTime);
        stats.put("totalOperations", totalOperations);
        
        // 最常用操作
        List<Object[]> commonOperations = operationLogRepository.findMostCommonOperations(
            org.springframework.data.domain.PageRequest.of(0, 10));
        stats.put("commonOperations", commonOperations);
        
        // 最活跃模块
        List<Object[]> activeModules = operationLogRepository.findMostActiveModules(
            org.springframework.data.domain.PageRequest.of(0, 10));
        stats.put("activeModules", activeModules);
        
        return stats;
    }

    @Override
    public Map<String, Object> getErrorLogStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 错误总数
        long totalErrors = errorLogRepository.countByCreatedAtBetween(startTime, endTime);
        stats.put("totalErrors", totalErrors);
        
        // 最常见错误类型
        List<Object[]> commonErrorTypes = errorLogRepository.findMostCommonErrorTypes(
            org.springframework.data.domain.PageRequest.of(0, 10));
        stats.put("commonErrorTypes", commonErrorTypes);
        
        // 最容易出错的模块
        List<Object[]> errorProneModules = errorLogRepository.findMostErrorProneModules(
            org.springframework.data.domain.PageRequest.of(0, 10));
        stats.put("errorProneModules", errorProneModules);
        
        // 严重程度分布
        List<Object[]> severityDistribution = errorLogRepository.findSeverityDistribution();
        stats.put("severityDistribution", severityDistribution);
        
        return stats;
    }

    @Override
    public Map<String, Object> getFileUploadStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 文件上传次数（通过版本创建次数统计，暂时注释，等待Repository方法实现）
        // long uploadCount = versionRepository.countByCreatedAtBetween(startTime, endTime);
        stats.put("uploadCount", 0L);
        
        // 各平台上传数量（暂时注释，等待Repository方法实现）
        // List<Object[]> platformUploads = versionRepository.findUploadsByPlatform(startTime, endTime);
        stats.put("platformUploads", new ArrayList<>());
        
        return stats;
    }

    @Override
    public List<Map<String, Object>> getPlatformDistribution() {
        // 暂时注释，等待Repository方法实现
        // List<Object[]> platformStats = applicationRepository.findPlatformDistribution();
        // return platformStats.stream().map(result -> {
        //     Map<String, Object> item = new HashMap<>();
        //     item.put("platform", result[0]);
        //     item.put("count", result[1]);
        //     return item;
        // }).collect(Collectors.toList());
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getVersionStatusDistribution() {
        // 暂时注释，等待Repository方法实现
        // List<Object[]> statusStats = versionRepository.findStatusDistribution();
        // return statusStats.stream().map(result -> {
        //     Map<String, Object> item = new HashMap<>();
        //     item.put("status", result[0]);
        //     item.put("count", result[1]);
        //     return item;
        // }).collect(Collectors.toList());
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getErrorSeverityDistribution() {
        List<Object[]> severityStats = errorLogRepository.findSeverityDistribution();
        return severityStats.stream().map(result -> {
            Map<String, Object> item = new HashMap<>();
            item.put("severity", result[0]);
            item.put("count", result[1]);
            return item;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getDailyActiveUsers(LocalDateTime startTime, LocalDateTime endTime) {
        // 这里需要实现按日期统计活跃用户的逻辑
        // 由于Repository中没有相应方法，暂时返回空列表
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getDailyOperations(LocalDateTime startTime, LocalDateTime endTime) {
        // 这里需要实现按日期统计操作次数的逻辑
        // 由于Repository中没有相应方法，暂时返回空列表
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getDailyErrors(LocalDateTime startTime, LocalDateTime endTime) {
        // 这里需要实现按日期统计错误次数的逻辑
        // 由于Repository中没有相应方法，暂时返回空列表
        return new ArrayList<>();
    }
} 