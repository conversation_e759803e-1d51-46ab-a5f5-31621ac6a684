package com.applicationcenter.backend.service.impl;

import com.applicationcenter.backend.config.FileUploadConfig;
import com.applicationcenter.backend.model.Application;
import com.applicationcenter.backend.model.Version;
import com.applicationcenter.backend.repository.ApplicationRepository;
import com.applicationcenter.backend.repository.VersionRepository;
import com.applicationcenter.backend.service.VersionService;
import com.applicationcenter.backend.util.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 版本服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@Transactional
public class VersionServiceImpl implements VersionService {
    
    @Autowired
    private VersionRepository versionRepository;
    
    @Autowired
    private ApplicationRepository applicationRepository;
    
    @Autowired
    @Qualifier("uploadPath")
    private String uploadPath;
    
    /**
     * 计算文件哈希值
     * 
     * @param file 文件
     * @return 哈希值
     */
    private String calculateFileHash(MultipartFile file) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(file.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException | IOException e) {
            throw new RuntimeException("计算文件哈希失败", e);
        }
    }
    
    /**
     * 保存文件到本地
     * 
     * @param file 文件
     * @param fileName 文件名
     * @return 文件路径
     */
    private String saveFile(MultipartFile file, String fileName) {
        try {
            // 创建上传目录
            Path uploadDir = Paths.get(uploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }
            
            // 生成文件路径
            Path filePath = uploadDir.resolve(fileName);
            
            // 保存文件
            Files.copy(file.getInputStream(), filePath);
            
            return filePath.toString();
        } catch (IOException e) {
            throw new RuntimeException("保存文件失败", e);
        }
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     */
    private void deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
            }
        } catch (IOException e) {
            // 记录日志但不抛出异常
            System.err.println("删除文件失败: " + filePath);
        }
    }
    
    @Override
    public ApiResponse<Object> createVersion(Version version, Integer adminId) {
        try {
            // 验证应用是否存在
            Application application = applicationRepository.findById(version.getApplication().getId())
                    .orElse(null);
            if (application == null) {
                return ApiResponse.error("应用不存在");
            }
            
            // 检查版本号是否已存在
            if (existsByVersionCode(application.getId(), version.getVersionCode())) {
                return ApiResponse.error("版本号已存在");
            }
            
            // 设置版本信息
            version.setApplication(application);
            version.setCreatedBy(adminId);
            version.setStatus(Version.Status.DRAFT);
            version.setCreatedAt(LocalDateTime.now());
            version.setUpdatedAt(LocalDateTime.now());
            
            // 设置默认值，允许创建草稿版本（file_path可以为空）
            if (version.getFilePath() == null) {
                version.setFilePath(null); // 明确设置为null，表示草稿版本
            }
            if (version.getFileSize() == null) {
                version.setFileSize(0L);
            }
            if (version.getIsForcedUpdate() == null) {
                version.setIsForcedUpdate(false);
            }
            
            // 保存版本
            Version savedVersion = versionRepository.save(version);
            
            return ApiResponse.success("版本创建成功", savedVersion);
        } catch (Exception e) {
            return ApiResponse.error("创建版本失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> uploadVersionFile(Integer versionId, MultipartFile file, Integer adminId) {
        try {
            // 查找版本
            Version version = versionRepository.findById(versionId)
                    .orElse(null);
            if (version == null) {
                return ApiResponse.error("版本不存在");
            }
            
            // 验证文件
            if (file.isEmpty()) {
                return ApiResponse.error("文件不能为空");
            }
            
            // 计算文件哈希
            String fileHash = calculateFileHash(file);
            
            // 检查文件是否已存在
            if (versionRepository.existsByFileHash(fileHash)) {
                return ApiResponse.error("文件已存在，请勿重复上传");
            }
            
            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = version.getApplication().getId() + "_" + version.getVersionCode() + extension;
            
            // 保存文件
            String filePath = saveFile(file, fileName);
            
            // 更新版本信息
            version.setFilePath(filePath);
            version.setFileSize(file.getSize());
            version.setFileHash(fileHash);
            version.setDownloadUrl(null); // 下载链接在发布时生成
            version.setUpdatedAt(LocalDateTime.now());
            
            // 保存版本
            versionRepository.save(version);
            
            return ApiResponse.success("文件上传成功", version);
        } catch (Exception e) {
            return ApiResponse.error("文件上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> updateVersion(Integer id, Version version) {
        try {
            Version existingVersion = versionRepository.findById(id)
                    .orElse(null);
            if (existingVersion == null) {
                return ApiResponse.error("版本不存在");
            }
            
            // 更新版本信息
            existingVersion.setVersionName(version.getVersionName());
            existingVersion.setDescription(version.getDescription());
            existingVersion.setMinRequiredVersion(version.getMinRequiredVersion());
            existingVersion.setReleaseNotes(version.getReleaseNotes());
            existingVersion.setIsForcedUpdate(version.getIsForcedUpdate());
            existingVersion.setUpdatedAt(LocalDateTime.now());
            
            // 保存版本
            Version updatedVersion = versionRepository.save(existingVersion);
            
            return ApiResponse.success("版本更新成功", updatedVersion);
        } catch (Exception e) {
            return ApiResponse.error("更新版本失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> deleteVersion(Integer id) {
        try {
            Version version = versionRepository.findById(id)
                    .orElse(null);
            if (version == null) {
                return ApiResponse.error("版本不存在");
            }
            
            // 删除文件
            if (version.getFilePath() != null) {
                deleteFile(version.getFilePath());
            }
            
            // 删除版本记录
            versionRepository.delete(version);
            
            return ApiResponse.success("版本删除成功");
        } catch (Exception e) {
            return ApiResponse.error("删除版本失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getVersion(Integer id) {
        try {
            Version version = versionRepository.findById(id)
                    .orElse(null);
            if (version == null) {
                return ApiResponse.error("版本不存在");
            }
            
            return ApiResponse.success("获取版本成功", version);
        } catch (Exception e) {
            return ApiResponse.error("获取版本失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getVersionsByApplication(Integer applicationId) {
        try {
            List<Version> versions = versionRepository.findByApplicationIdOrderByCreatedAtDesc(applicationId);
            return ApiResponse.success("获取版本列表成功", versions);
        } catch (Exception e) {
            return ApiResponse.error("获取版本列表失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getVersionsByApplication(Integer applicationId, Pageable pageable) {
        try {
            Page<Version> versions = versionRepository.findByApplicationIdOrderByCreatedAtDesc(applicationId, pageable);
            return ApiResponse.success("获取版本列表成功", versions);
        } catch (Exception e) {
            return ApiResponse.error("获取版本列表失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getLatestPublishedVersion(Integer applicationId) {
        try {
            Optional<Version> version = versionRepository.findFirstByApplicationIdAndStatusOrderByCreatedAtDesc(
                    applicationId, Version.Status.PUBLISHED);
            
            if (version.isPresent()) {
                return ApiResponse.success("获取最新版本成功", version.get());
            } else {
                return ApiResponse.error("未找到已发布的版本");
            }
        } catch (Exception e) {
            return ApiResponse.error("获取最新版本失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> publishVersion(Integer id) {
        try {
            Version version = versionRepository.findById(id)
                    .orElse(null);
            if (version == null) {
                return ApiResponse.error("版本不存在");
            }
            
            // 验证版本文件是否已上传
            if (version.getFilePath() == null || version.getFilePath().trim().isEmpty()) {
                return ApiResponse.error("版本文件未上传，无法发布。请先上传版本文件。");
            }
            
            // 验证文件大小
            if (version.getFileSize() == null || version.getFileSize() <= 0) {
                return ApiResponse.error("版本文件大小无效，无法发布。请重新上传文件。");
            }
            
            // 生成下载链接
            String downloadBaseUrl = "http://localhost:8080/api/versions/download"; // 假设下载基础URL
            String downloadUrl = downloadBaseUrl + "/" + version.getId();
            version.setDownloadUrl(downloadUrl);
            
            // 更新状态为已发布
            version.setStatus(Version.Status.PUBLISHED);
            version.setPublishedAt(LocalDateTime.now());
            version.setUpdatedAt(LocalDateTime.now());
            
            Version publishedVersion = versionRepository.save(version);
            
            return ApiResponse.success("版本发布成功", publishedVersion);
        } catch (Exception e) {
            return ApiResponse.error("发布版本失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> deprecateVersion(Integer id) {
        try {
            Version version = versionRepository.findById(id)
                    .orElse(null);
            if (version == null) {
                return ApiResponse.error("版本不存在");
            }
            
            // 更新状态为已下架
            version.setStatus(Version.Status.DEPRECATED);
            version.setUpdatedAt(LocalDateTime.now());
            
            Version deprecatedVersion = versionRepository.save(version);
            
            return ApiResponse.success("版本下架成功", deprecatedVersion);
        } catch (Exception e) {
            return ApiResponse.error("下架版本失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> updateVersionStatus(Integer id, Version.Status status) {
        try {
            Version version = versionRepository.findById(id)
                    .orElse(null);
            if (version == null) {
                return ApiResponse.error("版本不存在");
            }
            
            version.setStatus(status);
            if (status == Version.Status.PUBLISHED) {
                version.setPublishedAt(LocalDateTime.now());
            }
            version.setUpdatedAt(LocalDateTime.now());
            
            Version updatedVersion = versionRepository.save(version);
            
            return ApiResponse.success("版本状态更新成功", updatedVersion);
        } catch (Exception e) {
            return ApiResponse.error("更新版本状态失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> setForcedUpdate(Integer id, Boolean isForcedUpdate) {
        try {
            Version version = versionRepository.findById(id)
                    .orElse(null);
            if (version == null) {
                return ApiResponse.error("版本不存在");
            }
            
            version.setIsForcedUpdate(isForcedUpdate);
            version.setUpdatedAt(LocalDateTime.now());
            
            Version updatedVersion = versionRepository.save(version);
            
            return ApiResponse.success("强制更新设置成功", updatedVersion);
        } catch (Exception e) {
            return ApiResponse.error("设置强制更新失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> searchVersions(String keyword) {
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                return ApiResponse.error("搜索关键词不能为空");
            }
            
            List<Version> versions = versionRepository.searchVersions(keyword.trim());
            return ApiResponse.success("搜索版本成功", versions);
        } catch (Exception e) {
            return ApiResponse.error("搜索版本失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> searchVersions(String keyword, Pageable pageable) {
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                return ApiResponse.error("搜索关键词不能为空");
            }
            
            Page<Version> versions = versionRepository.searchVersions(keyword.trim(), pageable);
            return ApiResponse.success("搜索版本成功", versions);
        } catch (Exception e) {
            return ApiResponse.error("搜索版本失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getVersionsByStatus(Version.Status status) {
        try {
            List<Version> versions = versionRepository.findByStatusOrderByCreatedAtDesc(status);
            return ApiResponse.success("获取版本列表成功", versions);
        } catch (Exception e) {
            return ApiResponse.error("获取版本列表失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getVersionsByStatus(Version.Status status, Pageable pageable) {
        try {
            Page<Version> versions = versionRepository.findByStatusOrderByCreatedAtDesc(status, pageable);
            return ApiResponse.success("获取版本列表成功", versions);
        } catch (Exception e) {
            return ApiResponse.error("获取版本列表失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getVersionStatistics() {
        try {
            List<Object[]> statistics = versionRepository.getVersionStatistics();
            Map<String, Object> result = new HashMap<>();
            
            for (Object[] stat : statistics) {
                Version.Status status = (Version.Status) stat[0];
                Long count = (Long) stat[1];
                result.put(status.name(), count);
            }
            
            return ApiResponse.success("获取统计信息成功", result);
        } catch (Exception e) {
            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getVersionStatisticsByApplication(Integer applicationId) {
        try {
            List<Object[]> statistics = versionRepository.getVersionStatisticsByApplication(applicationId);
            Map<String, Object> result = new HashMap<>();
            
            for (Object[] stat : statistics) {
                Version.Status status = (Version.Status) stat[0];
                Long count = (Long) stat[1];
                result.put(status.name(), count);
            }
            
            return ApiResponse.success("获取应用版本统计信息成功", result);
        } catch (Exception e) {
            return ApiResponse.error("获取应用版本统计信息失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean existsByVersionCode(Integer applicationId, String versionCode) {
        return versionRepository.existsByApplicationIdAndVersionCode(applicationId, versionCode);
    }
    
    @Override
    public Version findById(Integer id) {
        return versionRepository.findById(id).orElse(null);
    }
    
    @Override
    public Version findByApplicationIdAndVersionCode(Integer applicationId, String versionCode) {
        return versionRepository.findByApplicationIdAndVersionCode(applicationId, versionCode).orElse(null);
    }
    
    @Override
    public ApiResponse<Object> getDownloadUrl(Integer id) {
        try {
            Version version = versionRepository.findById(id)
                    .orElse(null);
            if (version == null) {
                return ApiResponse.error("版本不存在");
            }
            
            if (version.getDownloadUrl() == null) {
                return ApiResponse.error("版本文件未上传");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("downloadUrl", version.getDownloadUrl());
            result.put("fileName", version.getFilePath() != null ? 
                    version.getFilePath().substring(version.getFilePath().lastIndexOf("/") + 1) : null);
            result.put("fileSize", version.getFileSize());
            
            return ApiResponse.success("获取下载链接成功", result);
        } catch (Exception e) {
            return ApiResponse.error("获取下载链接失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> checkForUpdate(Integer applicationId, String currentVersion) {
        try {
            // 获取最新发布的版本
            Optional<Version> latestVersion = versionRepository.findFirstByApplicationIdAndStatusOrderByCreatedAtDesc(
                    applicationId, Version.Status.PUBLISHED);
            
            if (!latestVersion.isPresent()) {
                return ApiResponse.error("未找到已发布的版本");
            }
            
            Version version = latestVersion.get();
            
            // 比较版本号（这里使用简单的字符串比较，实际项目中可能需要更复杂的版本比较逻辑）
            if (version.getVersionCode().equals(currentVersion)) {
                return ApiResponse.success("当前已是最新版本", null);
            }
            
            Map<String, Object> updateInfo = new HashMap<>();
            updateInfo.put("hasUpdate", true);
            updateInfo.put("latestVersion", version);
            updateInfo.put("isForcedUpdate", version.getIsForcedUpdate());
            
            return ApiResponse.success("发现新版本", updateInfo);
        } catch (Exception e) {
            return ApiResponse.error("检查更新失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Object> getForcedUpdateVersion(Integer applicationId) {
        try {
            List<Version> forcedVersions = versionRepository.findByApplicationIdAndIsForcedUpdateTrueOrderByCreatedAtDesc(applicationId);
            
            if (forcedVersions.isEmpty()) {
                return ApiResponse.success("没有强制更新版本", null);
            }
            
            // 返回最新的强制更新版本
            return ApiResponse.success("获取强制更新版本成功", forcedVersions.get(0));
        } catch (Exception e) {
            return ApiResponse.error("获取强制更新版本失败: " + e.getMessage());
        }
    }
} 