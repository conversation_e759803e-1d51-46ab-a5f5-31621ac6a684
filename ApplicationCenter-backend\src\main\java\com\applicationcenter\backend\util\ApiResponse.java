package com.applicationcenter.backend.util;

/**
 * 统一API响应格式
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class ApiResponse<T> {
    
    private String code;
    private String message;
    private T data;
    
    public ApiResponse() {}
    
    public ApiResponse(String code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public ApiResponse(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    // 成功响应
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>("200", "操作成功");
    }
    
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>("200", "操作成功", data);
    }
    
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>("200", message, data);
    }
    
    // 失败响应
    public static <T> ApiResponse<T> error(String code, String message) {
        return new ApiResponse<>(code, message);
    }
    
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>("500", message);
    }
    
    // Getter和Setter方法
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
} 