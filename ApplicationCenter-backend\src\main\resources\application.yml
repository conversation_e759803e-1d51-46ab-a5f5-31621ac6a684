server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: application-center-backend
  
  # 数据库配置
  datasource:
    url: *******************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    # SQL初始化配置
    sql-script-encoding: UTF-8
    initialization-mode: always
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update  # 改回update模式
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: false
        use_sql_comments: false
    defer-datasource-initialization: true
        
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true

# JWT配置
jwt:
  secret: application-center-jwt-secret-key-2024
  expiration: 86400000  # 24小时

# 文件存储配置
file:
  upload:
    path: ./uploads/
    allowed-types: apk,ipa,zip,rar,png,jpg,jpeg,gif
    max-size: 104857600  # 100MB

# 日志配置
logging:
  level:
    root: INFO
    org.springframework: INFO
    org.springframework.security: DEBUG
    org.springframework.web.cors: DEBUG
    org.springframework.web.filter: DEBUG
    org.hibernate: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql: WARN
    com.applicationcenter.backend: DEBUG
    com.applicationcenter.backend.config: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  # 启用Spring Security调试
  debug: true 