-- 插入默认管理员账号
-- 用户名: admin, 密码: admin123 (BCrypt加密后的密码)
INSERT INTO admin_users (username, password, status, created_at, updated_at) 
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 插入测试管理员账号
-- 用户名: test, 密码: test123 (BCrypt加密后的密码)
INSERT INTO admin_users (username, password, status, created_at, updated_at) 
VALUES ('test', '$2a$10$8K1p/a0dL1LXMIgoEDFrwOfgqwAG6SWaRqQKqHqHqHqHqHqHqHqHqHq', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 插入示例应用数据
INSERT INTO applications (name, package_name, platform, description, category, developer, status, created_by, created_at, updated_at) 
VALUES 
('示例应用1', 'com.example.app1', 'android', '这是一个示例Android应用', '工具', '示例开发者', 1, 1, NOW(), NOW()),
('示例应用2', 'com.example.app2', 'ios', '这是一个示例iOS应用', '娱乐', '示例开发者', 1, 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 插入示例版本数据
INSERT INTO versions (application_id, version_code, version_name, file_path, file_size, download_url, release_notes, status, created_by, created_at, updated_at, published_at) 
VALUES 
(1, '1.0.0', '初始版本', '/uploads/apps/1/versions/1.0.0.apk', 1024000, '/api/versions/download/1', '初始版本发布', 'PUBLISHED', 1, NOW(), NOW(), NOW()),
(1, '1.1.0', '功能更新', '/uploads/apps/1/versions/1.1.0.apk', 1056000, '/api/versions/download/2', '修复已知问题，优化性能', 'PUBLISHED', 1, NOW(), NOW(), NOW()),
(2, '1.0.0', '初始版本', '/uploads/apps/2/versions/1.0.0.ipa', 2048000, '/api/versions/download/3', '初始版本发布', 'PUBLISHED', 1, NOW(), NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW(); 