-- 数据库表结构初始化脚本
-- 如果表不存在则创建

-- 管理员表
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '管理员用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
    status TINYINT DEFAULT 1 COMMENT '状态(1启用/0禁用)',
    last_login DATETIME COMMENT '最后登录时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 应用表
CREATE TABLE IF NOT EXISTS applications (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
    name VARCHAR(100) NOT NULL COMMENT '应用名称',
    package_name VARCHAR(100) NOT NULL COMMENT '包名/Bundle ID',
    platform ENUM('ios','android','harmony') NOT NULL DEFAULT 'android' COMMENT '平台类型',
    description TEXT COMMENT '应用描述',
    icon VARCHAR(255) COMMENT '应用图标URL',
    category VARCHAR(50) COMMENT '应用分类',
    developer VARCHAR(100) COMMENT '开发者',
    website VARCHAR(255) COMMENT '官网地址',
    status TINYINT DEFAULT 1 COMMENT '状态(1启用/0禁用)',
    created_by INT COMMENT '创建人ID（管理员）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用表';

-- 版本表
CREATE TABLE IF NOT EXISTS versions (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
    application_id INT NOT NULL COMMENT '应用ID（外键）',
    version_code VARCHAR(50) NOT NULL COMMENT '版本号',
    version_name VARCHAR(100) NOT NULL COMMENT '版本名称',
    description TEXT COMMENT '版本描述',
    file_path VARCHAR(500) COMMENT '文件路径（可为空，用于草稿版本）',
    file_size BIGINT COMMENT '文件大小（字节）',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    download_url VARCHAR(500) COMMENT '下载链接',
    status ENUM('DRAFT','PUBLISHED','DEPRECATED') DEFAULT 'DRAFT' COMMENT '状态（草稿/已发布/已下架）',
    is_forced_update TINYINT DEFAULT 0 COMMENT '是否强制更新',
    min_required_version VARCHAR(50) COMMENT '最低要求版本',
    release_notes TEXT COMMENT '发布说明',
    created_by INT COMMENT '创建人ID（管理员）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    published_at DATETIME COMMENT '发布时间',
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    UNIQUE KEY uk_app_version (application_id, version_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
    admin_id INT NOT NULL COMMENT '管理员ID（外键）',
    username VARCHAR(50) COMMENT '用户名',
    operation VARCHAR(100) NOT NULL COMMENT '操作名称',
    module VARCHAR(50) COMMENT '模块名称',
    description VARCHAR(500) COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_params TEXT COMMENT '请求参数',
    response_status INT COMMENT '响应状态码',
    execution_time BIGINT COMMENT '执行时间（毫秒）',
    log_level VARCHAR(20) DEFAULT 'INFO' COMMENT '日志级别',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (admin_id) REFERENCES admin_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 错误日志表
CREATE TABLE IF NOT EXISTS error_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
    error_type VARCHAR(100) NOT NULL COMMENT '错误类型',
    error_message TEXT NOT NULL COMMENT '错误消息',
    stack_trace TEXT COMMENT '堆栈跟踪',
    severity VARCHAR(20) DEFAULT 'ERROR' COMMENT '严重程度',
    source VARCHAR(100) COMMENT '错误来源',
    user_id INT COMMENT '用户ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_params TEXT COMMENT '请求参数',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES admin_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错误日志表'; 