package com.applicationcenter.backend.aspect;

import com.applicationcenter.backend.annotation.LogOperation;
import com.applicationcenter.backend.model.OperationLog;
import com.applicationcenter.backend.service.OperationLogService;
import com.applicationcenter.backend.util.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 日志切面测试类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class LogAspectTest {

    @Mock
    private OperationLogService operationLogService;

    @Mock
    private Authentication authentication;

    @Mock
    private SecurityContext securityContext;

    @InjectMocks
    private LogAspect logAspect;

    private TestController testController;

    @BeforeEach
    void setUp() {
        testController = new TestController();
        
        // 设置SecurityContext
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.isAuthenticated()).thenReturn(true);
        when(authentication.getName()).thenReturn("testuser");
        SecurityContextHolder.setContext(securityContext);
        
        // 设置RequestContext
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/test");
        request.setMethod("GET");
        request.setRemoteAddr("127.0.0.1");
        request.addHeader("User-Agent", "TestAgent");
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
    }

    /**
     * 测试正常方法调用
     */
    @Test
    void testNormalMethodCall() throws Throwable {
        // 准备
        when(operationLogService.save(any(OperationLog.class))).thenReturn(new OperationLog());

        // 执行
        ApiResponse<String> result = testController.testMethod("testParam");

        // 验证
        verify(operationLogService, timeout(1000)).save(any(OperationLog.class));
        assert result.getCode().equals("200");
    }

    /**
     * 测试异常方法调用
     */
    @Test
    void testExceptionMethodCall() {
        // 准备
        when(operationLogService.save(any(OperationLog.class))).thenReturn(new OperationLog());

        // 执行并验证异常
        try {
            testController.testExceptionMethod();
        } catch (RuntimeException e) {
            // 预期异常
        }

        // 验证日志记录
        verify(operationLogService, timeout(1000)).save(any(OperationLog.class));
    }

    /**
     * 测试无注解方法调用
     */
    @Test
    void testMethodWithoutAnnotation() throws Throwable {
        // 执行
        String result = testController.methodWithoutAnnotation();

        // 验证不记录日志
        verify(operationLogService, never()).save(any(OperationLog.class));
        assert "success".equals(result);
    }

    /**
     * 测试日志记录失败不影响主业务流程
     */
    @Test
    void testLogServiceFailure() throws Throwable {
        // 准备 - 模拟日志服务失败
        when(operationLogService.save(any(OperationLog.class))).thenThrow(new RuntimeException("Log service error"));

        // 执行
        ApiResponse<String> result = testController.testMethod("testParam");

        // 验证主业务流程正常执行
        assert result.getCode().equals("200");
        assert "success".equals(result.getData());
    }

    /**
     * 测试不同日志级别
     */
    @Test
    void testDifferentLogLevels() throws Throwable {
        // 准备
        when(operationLogService.save(any(OperationLog.class))).thenReturn(new OperationLog());

        // 执行不同级别的方法
        testController.testInfoMethod();
        testController.testWarnMethod();
        testController.testErrorMethod();

        // 验证都记录了日志
        verify(operationLogService, timeout(1000).times(3)).save(any(OperationLog.class));
    }

    /**
     * 测试Controller方法
     */
    @Test
    void testControllerMethod() throws Throwable {
        // 准备
        when(operationLogService.save(any(OperationLog.class))).thenReturn(new OperationLog());

        // 执行
        ApiResponse<String> result = testController.controllerMethod();

        // 验证
        verify(operationLogService, timeout(1000)).save(any(OperationLog.class));
        assert result.getCode().equals("200");
    }

    /**
     * 测试方法 - 用于测试的Controller类
     */
    public static class TestController {

        @LogOperation(description = "测试方法", module = "测试模块", level = LogOperation.LogLevel.INFO)
        public ApiResponse<String> testMethod(String param) {
            return ApiResponse.success("success");
        }

        @LogOperation(description = "测试异常方法", module = "测试模块", level = LogOperation.LogLevel.ERROR)
        public void testExceptionMethod() {
            throw new RuntimeException("测试异常");
        }

        public String methodWithoutAnnotation() {
            return "success";
        }

        @LogOperation(description = "信息级别方法", module = "测试模块", level = LogOperation.LogLevel.INFO)
        public void testInfoMethod() {
            // 空方法
        }

        @LogOperation(description = "警告级别方法", module = "测试模块", level = LogOperation.LogLevel.WARN)
        public void testWarnMethod() {
            // 空方法
        }

        @LogOperation(description = "错误级别方法", module = "测试模块", level = LogOperation.LogLevel.ERROR)
        public void testErrorMethod() {
            // 空方法
        }

        @LogOperation(description = "Controller方法", module = "Controller", level = LogOperation.LogLevel.INFO)
        public ApiResponse<String> controllerMethod() {
            return ApiResponse.success("controller success");
        }
    }
} 