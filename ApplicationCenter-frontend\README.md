# ApplicationCenter Frontend

APP版本管理系统前端项目

## 技术栈

- **核心框架**: Vue 3.5+
- **构建工具**: Vite 7.0+
- **UI组件库**: Element Plus 2.10+
- **路由管理**: Vue Router 4.2+
- **状态管理**: Pinia 2.1+
- **HTTP客户端**: Axios 1.6+
- **CSS预处理器**: SCSS
- **代码规范**: ESLint + Prettier
- **TypeScript**: 5.0+

## 项目结构

```
src/
├── assets/               # 静态资源
├── components/           # 公共组件
│   ├── common/          # 通用组件
│   ├── layout/          # 布局组件
│   └── business/        # 业务组件
├── views/               # 页面组件
│   ├── home/           # 应用下载中心
│   ├── admin/          # 管理员后台
│   ├── api-docs/       # API文档
│   └── common/         # 通用页面
├── router/              # 路由配置
├── stores/              # 状态管理
├── utils/               # 工具函数
├── styles/              # 样式文件
├── App.vue             # 根组件
└── main.ts             # 入口文件
```

## 环境要求

- Node.js 16+
- npm 8+

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 构建生产版本
```bash
npm run build
```

### 4. 预览生产版本
```bash
npm run preview
```

## 主要功能

### 应用下载中心（公开页面）
- 应用列表展示
- 应用搜索和筛选
- 应用详情页
- 版本下载功能

### 管理员后台（需要认证）
- 管理员登录
- 应用管理（增删改查）
- 版本管理（上传、发布、下架）
- 用户管理
- 操作日志
- 数据统计

### API文档页面（公开页面）
- 接口文档展示
- 在线测试功能

## 开发说明

### 路由配置
项目使用Vue Router进行路由管理，主要路由包括：
- `/` - 应用下载中心首页
- `/app/:id` - 应用详情页
- `/admin` - 管理员后台
- `/login` - 管理员登录
- `/api-docs` - API文档

### 状态管理
使用Pinia进行状态管理，主要store包括：
- `user` - 用户认证状态
- `app` - 应用相关状态

### HTTP请求
封装了Axios请求工具，支持：
- 统一请求拦截
- 统一响应处理
- 错误处理
- 文件上传

### 样式系统
- 使用SCSS预处理器
- 全局变量定义
- 响应式设计
- Element Plus主题定制

## 配置说明

### Vite配置
- 路径别名配置
- 开发服务器代理
- SCSS全局变量注入

### TypeScript配置
- 严格模式
- 路径映射
- Vue组件类型支持

### ESLint配置
- Vue 3规则
- TypeScript规则
- Prettier集成

## 部署

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
npm run build
```

构建后的文件位于 `dist/` 目录，可部署到任何静态文件服务器。

## 与后端集成

前端通过代理配置与后端API集成：
- 开发环境：`/api` 代理到 `http://localhost:8080/api`
- 生产环境：需要配置正确的API地址

## 联系方式

如有问题，请联系开发团队。
