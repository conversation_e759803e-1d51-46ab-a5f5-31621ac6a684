{"name": "app.center", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/node": "^24.1.0", "@vue/tsconfig": "^0.7.0", "axios": "^1.11.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "typescript": "^5.8.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "eslint": "^9.32.0", "eslint-plugin-vue": "^10.3.0", "prettier": "^3.6.2", "sass": "^1.89.2", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^3.0.4"}}