"use strict";
/// <reference types="D:/桌面/临时文件/A源码/APP版本管理系统重构/ApplicationCenter-frontend/node_modules/.vue-global-types/vue_3.5_0.d.ts" />
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var __VLS_props = defineProps({
    msg: {
        type: String,
        required: true,
    },
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
var __VLS_ctx = {};
var __VLS_elements;
var __VLS_components;
var __VLS_directives;
/** @type {__VLS_StyleScopedClasses['greetings']} */ ;
/** @type {__VLS_StyleScopedClasses['greetings']} */ ;
/** @type {__VLS_StyleScopedClasses['greetings']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_elements.div, __VLS_elements.div)(__assign({ class: "greetings" }));
__VLS_asFunctionalElement(__VLS_elements.h1, __VLS_elements.h1)(__assign({ class: "green" }));
(__VLS_ctx.msg);
// @ts-ignore
[msg,];
__VLS_asFunctionalElement(__VLS_elements.h3, __VLS_elements.h3)({});
__VLS_asFunctionalElement(__VLS_elements.a, __VLS_elements.a)({
    href: "https://vite.dev/",
    target: "_blank",
    rel: "noopener",
});
__VLS_asFunctionalElement(__VLS_elements.a, __VLS_elements.a)({
    href: "https://vuejs.org/",
    target: "_blank",
    rel: "noopener",
});
/** @type {__VLS_StyleScopedClasses['greetings']} */ ;
/** @type {__VLS_StyleScopedClasses['green']} */ ;
var __VLS_dollars;
var __VLS_self = (await Promise.resolve().then(function () { return require('vue'); })).defineComponent({
    setup: function () {
        return __assign({ $props: __VLS_makeOptional(__VLS_props) }, __VLS_props);
    },
});
exports.default = (await Promise.resolve().then(function () { return require('vue'); })).defineComponent({
    setup: function () {
        var __VLS_returns = __assign({ $props: __VLS_makeOptional(__VLS_props) }, __VLS_props);
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
