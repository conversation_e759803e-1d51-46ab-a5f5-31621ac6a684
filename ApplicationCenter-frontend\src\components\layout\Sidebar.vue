<template>
  <el-aside :width="isCollapse ? '64px' : '200px'" class="app-sidebar">
    <el-menu
      :default-active="activeMenu"
      :collapse="isCollapse"
      :unique-opened="true"
      router
      class="sidebar-menu"
    >
      <el-menu-item index="/admin">
        <el-icon><DataBoard /></el-icon>
        <template #title>仪表盘</template>
      </el-menu-item>
      
      <el-menu-item index="/admin/apps">
        <el-icon><Grid /></el-icon>
        <template #title>应用管理</template>
      </el-menu-item>
      
      <el-menu-item index="/admin/versions">
        <el-icon><Files /></el-icon>
        <template #title>版本管理</template>
      </el-menu-item>
      
      <el-menu-item index="/admin/users">
        <el-icon><User /></el-icon>
        <template #title>管理员管理</template>
      </el-menu-item>
      
      <el-menu-item index="/admin/logs">
        <el-icon><Document /></el-icon>
        <template #title>操作日志</template>
      </el-menu-item>
      
      <el-menu-item index="/admin/settings">
        <el-icon><Setting /></el-icon>
        <template #title>系统设置</template>
      </el-menu-item>
    </el-menu>
    
    <div class="sidebar-toggle" @click="toggleCollapse">
      <el-icon>
        <component :is="isCollapse ? 'Expand' : 'Fold'" />
      </el-icon>
    </div>
  </el-aside>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  DataBoard,
  Grid,
  Files,
  User,
  Document,
  Setting,
  Expand,
  Fold
} from '@element-plus/icons-vue'

const route = useRoute()
const isCollapse = ref(false)

// 当前激活的菜单项
const activeMenu = computed(() => {
  return route.path
})

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style scoped lang="scss">
.app-sidebar {
  background-color: #fff;
  border-right: 1px solid #e4e7ed;
  position: fixed;
  top: 60px;
  left: 0;
  bottom: 0;
  z-index: 999;
  transition: width 0.3s;
}

.sidebar-menu {
  height: calc(100% - 50px);
  border-right: none;
}

.sidebar-toggle {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-top: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #ebeef5;
  }
  
  .el-icon {
    font-size: 16px;
    color: #909399;
  }
}

:deep(.el-menu) {
  border-right: none;
}

:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  
  &.is-active {
    background-color: #409eff;
    color: #fff;
    
    .el-icon {
      color: #fff;
    }
  }
  
  &:hover {
    background-color: #f5f7fa;
  }
}

:deep(.el-menu--collapse) {
  .el-menu-item {
    .el-icon {
      margin-right: 0;
    }
  }
}
</style> 