"use strict";
/// <reference types="D:/桌面/临时文件/A源码/APP版本管理系统重构/ApplicationCenter-frontend/node_modules/.vue-global-types/vue_3.5_0.d.ts" />
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
var vue_1 = require("vue");
var vue_router_1 = require("vue-router");
var icons_vue_1 = require("@element-plus/icons-vue");
var route = (0, vue_router_1.useRoute)();
var isCollapse = (0, vue_1.ref)(false);
// 当前激活的菜单项
var activeMenu = (0, vue_1.computed)(function () {
    return route.path;
});
// 切换侧边栏折叠状态
var toggleCollapse = function () {
    isCollapse.value = !isCollapse.value;
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
var __VLS_ctx = {};
var __VLS_elements;
var __VLS_components;
var __VLS_directives;
/** @type {__VLS_StyleScopedClasses['el-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['el-menu-item']} */ ;
/** @type {__VLS_StyleScopedClasses['el-icon']} */ ;
// CSS variable injection 
// CSS variable injection end 
var __VLS_0 = {}.ElAside;
/** @type {[typeof __VLS_components.ElAside, typeof __VLS_components.elAside, typeof __VLS_components.ElAside, typeof __VLS_components.elAside, ]} */ ;
// @ts-ignore
ElAside;
// @ts-ignore
var __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0(__assign({ width: (__VLS_ctx.isCollapse ? '64px' : '200px') }, { class: "app-sidebar" })));
var __VLS_2 = __VLS_1.apply(void 0, __spreadArray([__assign({ width: (__VLS_ctx.isCollapse ? '64px' : '200px') }, { class: "app-sidebar" })], __VLS_functionalComponentArgsRest(__VLS_1), false));
var __VLS_4 = {};
var __VLS_5 = __VLS_3.slots.default;
// @ts-ignore
[isCollapse,];
var __VLS_6 = {}.ElMenu;
/** @type {[typeof __VLS_components.ElMenu, typeof __VLS_components.elMenu, typeof __VLS_components.ElMenu, typeof __VLS_components.elMenu, ]} */ ;
// @ts-ignore
ElMenu;
// @ts-ignore
var __VLS_7 = __VLS_asFunctionalComponent(__VLS_6, new __VLS_6(__assign({ defaultActive: (__VLS_ctx.activeMenu), collapse: (__VLS_ctx.isCollapse), uniqueOpened: (true), router: true }, { class: "sidebar-menu" })));
var __VLS_8 = __VLS_7.apply(void 0, __spreadArray([__assign({ defaultActive: (__VLS_ctx.activeMenu), collapse: (__VLS_ctx.isCollapse), uniqueOpened: (true), router: true }, { class: "sidebar-menu" })], __VLS_functionalComponentArgsRest(__VLS_7), false));
var __VLS_10 = __VLS_9.slots.default;
// @ts-ignore
[isCollapse, activeMenu,];
var __VLS_11 = {}.ElMenuItem;
/** @type {[typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, ]} */ ;
// @ts-ignore
ElMenuItem;
// @ts-ignore
var __VLS_12 = __VLS_asFunctionalComponent(__VLS_11, new __VLS_11({
    index: "/admin",
}));
var __VLS_13 = __VLS_12.apply(void 0, __spreadArray([{
        index: "/admin",
    }], __VLS_functionalComponentArgsRest(__VLS_12), false));
var __VLS_15 = __VLS_14.slots.default;
var __VLS_16 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
ElIcon;
// @ts-ignore
var __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({}));
var __VLS_18 = __VLS_17.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_17), false));
var __VLS_20 = __VLS_19.slots.default;
var __VLS_21 = {}.DataBoard;
/** @type {[typeof __VLS_components.DataBoard, ]} */ ;
// @ts-ignore
icons_vue_1.DataBoard;
// @ts-ignore
var __VLS_22 = __VLS_asFunctionalComponent(__VLS_21, new __VLS_21({}));
var __VLS_23 = __VLS_22.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_22), false));
var __VLS_19;
{
    var __VLS_26 = __VLS_14.slots.title;
}
var __VLS_14;
var __VLS_27 = {}.ElMenuItem;
/** @type {[typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, ]} */ ;
// @ts-ignore
ElMenuItem;
// @ts-ignore
var __VLS_28 = __VLS_asFunctionalComponent(__VLS_27, new __VLS_27({
    index: "/admin/apps",
}));
var __VLS_29 = __VLS_28.apply(void 0, __spreadArray([{
        index: "/admin/apps",
    }], __VLS_functionalComponentArgsRest(__VLS_28), false));
var __VLS_31 = __VLS_30.slots.default;
var __VLS_32 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
ElIcon;
// @ts-ignore
var __VLS_33 = __VLS_asFunctionalComponent(__VLS_32, new __VLS_32({}));
var __VLS_34 = __VLS_33.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_33), false));
var __VLS_36 = __VLS_35.slots.default;
var __VLS_37 = {}.Grid;
/** @type {[typeof __VLS_components.Grid, ]} */ ;
// @ts-ignore
icons_vue_1.Grid;
// @ts-ignore
var __VLS_38 = __VLS_asFunctionalComponent(__VLS_37, new __VLS_37({}));
var __VLS_39 = __VLS_38.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_38), false));
var __VLS_35;
{
    var __VLS_42 = __VLS_30.slots.title;
}
var __VLS_30;
var __VLS_43 = {}.ElMenuItem;
/** @type {[typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, ]} */ ;
// @ts-ignore
ElMenuItem;
// @ts-ignore
var __VLS_44 = __VLS_asFunctionalComponent(__VLS_43, new __VLS_43({
    index: "/admin/versions",
}));
var __VLS_45 = __VLS_44.apply(void 0, __spreadArray([{
        index: "/admin/versions",
    }], __VLS_functionalComponentArgsRest(__VLS_44), false));
var __VLS_47 = __VLS_46.slots.default;
var __VLS_48 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
ElIcon;
// @ts-ignore
var __VLS_49 = __VLS_asFunctionalComponent(__VLS_48, new __VLS_48({}));
var __VLS_50 = __VLS_49.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_49), false));
var __VLS_52 = __VLS_51.slots.default;
var __VLS_53 = {}.Files;
/** @type {[typeof __VLS_components.Files, ]} */ ;
// @ts-ignore
icons_vue_1.Files;
// @ts-ignore
var __VLS_54 = __VLS_asFunctionalComponent(__VLS_53, new __VLS_53({}));
var __VLS_55 = __VLS_54.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_54), false));
var __VLS_51;
{
    var __VLS_58 = __VLS_46.slots.title;
}
var __VLS_46;
var __VLS_59 = {}.ElMenuItem;
/** @type {[typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, ]} */ ;
// @ts-ignore
ElMenuItem;
// @ts-ignore
var __VLS_60 = __VLS_asFunctionalComponent(__VLS_59, new __VLS_59({
    index: "/admin/users",
}));
var __VLS_61 = __VLS_60.apply(void 0, __spreadArray([{
        index: "/admin/users",
    }], __VLS_functionalComponentArgsRest(__VLS_60), false));
var __VLS_63 = __VLS_62.slots.default;
var __VLS_64 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
ElIcon;
// @ts-ignore
var __VLS_65 = __VLS_asFunctionalComponent(__VLS_64, new __VLS_64({}));
var __VLS_66 = __VLS_65.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_65), false));
var __VLS_68 = __VLS_67.slots.default;
var __VLS_69 = {}.User;
/** @type {[typeof __VLS_components.User, ]} */ ;
// @ts-ignore
icons_vue_1.User;
// @ts-ignore
var __VLS_70 = __VLS_asFunctionalComponent(__VLS_69, new __VLS_69({}));
var __VLS_71 = __VLS_70.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_70), false));
var __VLS_67;
{
    var __VLS_74 = __VLS_62.slots.title;
}
var __VLS_62;
var __VLS_75 = {}.ElMenuItem;
/** @type {[typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, ]} */ ;
// @ts-ignore
ElMenuItem;
// @ts-ignore
var __VLS_76 = __VLS_asFunctionalComponent(__VLS_75, new __VLS_75({
    index: "/admin/logs",
}));
var __VLS_77 = __VLS_76.apply(void 0, __spreadArray([{
        index: "/admin/logs",
    }], __VLS_functionalComponentArgsRest(__VLS_76), false));
var __VLS_79 = __VLS_78.slots.default;
var __VLS_80 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
ElIcon;
// @ts-ignore
var __VLS_81 = __VLS_asFunctionalComponent(__VLS_80, new __VLS_80({}));
var __VLS_82 = __VLS_81.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_81), false));
var __VLS_84 = __VLS_83.slots.default;
var __VLS_85 = {}.Document;
/** @type {[typeof __VLS_components.Document, ]} */ ;
// @ts-ignore
icons_vue_1.Document;
// @ts-ignore
var __VLS_86 = __VLS_asFunctionalComponent(__VLS_85, new __VLS_85({}));
var __VLS_87 = __VLS_86.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_86), false));
var __VLS_83;
{
    var __VLS_90 = __VLS_78.slots.title;
}
var __VLS_78;
var __VLS_91 = {}.ElMenuItem;
/** @type {[typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, ]} */ ;
// @ts-ignore
ElMenuItem;
// @ts-ignore
var __VLS_92 = __VLS_asFunctionalComponent(__VLS_91, new __VLS_91({
    index: "/admin/settings",
}));
var __VLS_93 = __VLS_92.apply(void 0, __spreadArray([{
        index: "/admin/settings",
    }], __VLS_functionalComponentArgsRest(__VLS_92), false));
var __VLS_95 = __VLS_94.slots.default;
var __VLS_96 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
ElIcon;
// @ts-ignore
var __VLS_97 = __VLS_asFunctionalComponent(__VLS_96, new __VLS_96({}));
var __VLS_98 = __VLS_97.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_97), false));
var __VLS_100 = __VLS_99.slots.default;
var __VLS_101 = {}.Setting;
/** @type {[typeof __VLS_components.Setting, ]} */ ;
// @ts-ignore
icons_vue_1.Setting;
// @ts-ignore
var __VLS_102 = __VLS_asFunctionalComponent(__VLS_101, new __VLS_101({}));
var __VLS_103 = __VLS_102.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_102), false));
var __VLS_99;
{
    var __VLS_106 = __VLS_94.slots.title;
}
var __VLS_94;
var __VLS_9;
__VLS_asFunctionalElement(__VLS_elements.div, __VLS_elements.div)(__assign({ onClick: (__VLS_ctx.toggleCollapse) }, { class: "sidebar-toggle" }));
// @ts-ignore
[toggleCollapse,];
var __VLS_107 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
ElIcon;
// @ts-ignore
var __VLS_108 = __VLS_asFunctionalComponent(__VLS_107, new __VLS_107({}));
var __VLS_109 = __VLS_108.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_108), false));
var __VLS_111 = __VLS_110.slots.default;
var __VLS_112 = ((__VLS_ctx.isCollapse ? 'Expand' : 'Fold'));
// @ts-ignore
var __VLS_113 = __VLS_asFunctionalComponent(__VLS_112, new __VLS_112({}));
var __VLS_114 = __VLS_113.apply(void 0, __spreadArray([{}], __VLS_functionalComponentArgsRest(__VLS_113), false));
// @ts-ignore
[isCollapse,];
var __VLS_110;
var __VLS_3;
/** @type {__VLS_StyleScopedClasses['app-sidebar']} */ ;
/** @type {__VLS_StyleScopedClasses['sidebar-menu']} */ ;
/** @type {__VLS_StyleScopedClasses['sidebar-toggle']} */ ;
var __VLS_dollars;
var __VLS_self = (await Promise.resolve().then(function () { return require('vue'); })).defineComponent({
    setup: function () {
        return {
            DataBoard: icons_vue_1.DataBoard,
            Grid: icons_vue_1.Grid,
            Files: icons_vue_1.Files,
            User: icons_vue_1.User,
            Document: icons_vue_1.Document,
            Setting: icons_vue_1.Setting,
            isCollapse: isCollapse,
            activeMenu: activeMenu,
            toggleCollapse: toggleCollapse,
        };
    },
});
exports.default = (await Promise.resolve().then(function () { return require('vue'); })).defineComponent({
    setup: function () {
    },
});
; /* PartiallyEnd: #4569/main.vue */
