import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/home/<USER>'),
    meta: {
      title: '应用下载中心',
      requiresAuth: false
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/admin/Login.vue'),
    meta: {
      title: '管理员登录',
      requiresAuth: false
    }
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/views/admin/index.vue'),
    meta: {
      title: '管理员后台',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/Dashboard.vue'),
        meta: {
          title: '仪表盘',
          requiresAuth: true
        }
      },
      {
        path: 'apps',
        name: 'AppManagement',
        component: () => import('@/views/admin/AppManagement.vue'),
        meta: {
          title: '应用管理',
          requiresAuth: true
        }
      },
      {
        path: 'versions',
        name: 'VersionManagement',
        component: () => import('@/views/admin/VersionManagement.vue'),
        meta: {
          title: '版本管理',
          requiresAuth: true
        }
      },
      {
        path: 'statistics/downloads',
        name: 'DownloadsStatistics',
        component: () => import('@/views/admin/statistics/Downloads.vue'),
        meta: {
          title: '下载统计',
          requiresAuth: true
        }
      },
      {
        path: 'statistics/users',
        name: 'UsersStatistics',
        component: () => import('@/views/admin/statistics/Users.vue'),
        meta: {
          title: '用户统计',
          requiresAuth: true
        }
      },
      {
        path: 'system/users',
        name: 'SystemUsers',
        component: () => import('@/views/admin/system/Users.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true
        }
      },
      {
        path: 'system/logs',
        name: 'SystemLogs',
        component: () => import('@/views/admin/system/Logs.vue'),
        meta: {
          title: '系统日志',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/api-docs',
    name: 'ApiDocs',
    component: () => import('@/views/api-docs/index.vue'),
    meta: {
      title: 'API文档',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/common/NotFound.vue'),
    meta: {
      title: '页面未找到',
      requiresAuth: false
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - APP版本管理系统` : 'APP版本管理系统'
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      next('/login')
      return
    }
  }
  
  next()
})

export default router 