import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('admin_token') || '',
    username: localStorage.getItem('admin_username') || '',
    isLoggedIn: !!localStorage.getItem('admin_token')
  }),
  
  actions: {
    setToken(token) {
      this.token = token
      this.isLoggedIn = true
      localStorage.setItem('admin_token', token)
    },
    
    setUsername(username) {
      this.username = username
      localStorage.setItem('admin_username', username)
    },
    
    clearAuth() {
      this.token = ''
      this.username = ''
      this.isLoggedIn = false
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_username')
    },
    
    logout() {
      this.clearAuth()
    }
  }
})
