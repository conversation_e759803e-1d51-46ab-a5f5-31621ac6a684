<template>
  <div class="app-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">应用管理</h1>
        <p class="page-description">管理系统中的应用信息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="Plus" @click="showAddDialog">
          添加应用
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card" shadow="never">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索应用名称"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="platformFilter" placeholder="平台" clearable @change="handleFilter">
            <el-option label="全部平台" value="" />
            <el-option label="Android" value="ANDROID" />
            <el-option label="iOS" value="IOS" />
            <el-option label="Windows" value="WINDOWS" />
            <el-option label="macOS" value="MACOS" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态" clearable @change="handleFilter">
            <el-option label="全部状态" value="" />
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="停用" value="INACTIVE" />
          </el-select>
        </el-col>
        <el-col :span="10">
          <div class="filter-actions">
            <el-button @click="resetFilters">重置</el-button>
            <el-button type="primary" @click="refreshData">刷新</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 应用列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="filteredApps"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="应用信息" min-width="300">
          <template #default="{ row }">
            <div class="app-info">
              <el-image
                :src="row.iconUrl || '/default-app-icon.png'"
                class="app-icon"
                fit="cover"
              >
                <template #error>
                  <div class="icon-placeholder">
                    <el-icon><App /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="app-details">
                <div class="app-name">{{ row.name }}</div>
                <div class="app-description">{{ row.description }}</div>
                <div class="app-meta">
                  <el-tag size="small" :type="getPlatformType(row.platform)">
                    {{ getPlatformLabel(row.platform) }}
                  </el-tag>
                  <span class="version">v{{ row.latestVersion }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="分类" width="100" />
        
        <el-table-column prop="downloadCount" label="下载量" width="100" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.downloadCount) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
              {{ row.status === 'ACTIVE' ? '活跃' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewApp(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editApp(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteApp(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalApps"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量操作 -->
    <div v-if="selectedApps.length > 0" class="batch-actions">
      <el-card shadow="never">
        <div class="batch-content">
          <span>已选择 {{ selectedApps.length }} 个应用</span>
          <div class="batch-buttons">
            <el-button size="small" @click="batchEnable">批量启用</el-button>
            <el-button size="small" type="warning" @click="batchDisable">批量停用</el-button>
            <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 添加/编辑应用对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加应用' : '编辑应用'"
      width="600px"
    >
      <el-form
        ref="appFormRef"
        :model="appForm"
        :rules="appRules"
        label-width="100px"
      >
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="appForm.name" placeholder="请输入应用名称" />
        </el-form-item>
        
        <el-form-item label="应用描述" prop="description">
          <el-input
            v-model="appForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入应用描述"
          />
        </el-form-item>
        
        <el-form-item label="平台" prop="platform">
          <el-select v-model="appForm.platform" placeholder="选择平台">
            <el-option label="Android" value="ANDROID" />
            <el-option label="iOS" value="IOS" />
            <el-option label="Windows" value="WINDOWS" />
            <el-option label="macOS" value="MACOS" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分类" prop="category">
          <el-input v-model="appForm.category" placeholder="请输入分类" />
        </el-form-item>
        
        <el-form-item label="应用图标" prop="iconUrl">
          <el-upload
            class="icon-uploader"
            action="/api/upload"
            :show-file-list="false"
            :on-success="handleIconSuccess"
            :before-upload="beforeIconUpload"
          >
            <img v-if="appForm.iconUrl" :src="appForm.iconUrl" class="uploaded-icon" />
            <el-icon v-else class="uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="appForm.status">
            <el-radio label="ACTIVE">活跃</el-radio>
            <el-radio label="INACTIVE">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitApp" :loading="submitting">
            {{ dialogType === 'add' ? '添加' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 应用详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="应用详情"
      width="800px"
    >
      <div v-if="selectedAppDetail" class="app-detail">
        <div class="detail-header">
          <el-image
            :src="selectedAppDetail.iconUrl || '/default-app-icon.png'"
            class="detail-icon"
            fit="cover"
          />
          <div class="detail-info">
            <h2>{{ selectedAppDetail.name }}</h2>
            <p>{{ selectedAppDetail.description }}</p>
            <div class="detail-meta">
              <el-tag :type="getPlatformType(selectedAppDetail.platform)">
                {{ getPlatformLabel(selectedAppDetail.platform) }}
              </el-tag>
              <el-tag :type="selectedAppDetail.status === 'ACTIVE' ? 'success' : 'danger'">
                {{ selectedAppDetail.status === 'ACTIVE' ? '活跃' : '停用' }}
              </el-tag>
              <span class="category">{{ selectedAppDetail.category }}</span>
            </div>
          </div>
        </div>
        
        <el-divider />
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="最新版本">
            v{{ selectedAppDetail.latestVersion }}
          </el-descriptions-item>
          <el-descriptions-item label="下载量">
            {{ formatNumber(selectedAppDetail.downloadCount) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(selectedAppDetail.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(selectedAppDetail.updateTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, App } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const platformFilter = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalApps = ref(0)
const selectedApps = ref([])
const dialogVisible = ref(false)
const dialogType = ref('add')
const detailDialogVisible = ref(false)
const selectedAppDetail = ref(null)
const submitting = ref(false)

const appFormRef = ref()

// 表单数据
const appForm = reactive({
  name: '',
  description: '',
  platform: '',
  category: '',
  iconUrl: '',
  status: 'ACTIVE'
})

// 表单验证规则
const appRules = {
  name: [
    { required: true, message: '请输入应用名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入应用描述', trigger: 'blur' },
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请输入分类', trigger: 'blur' }
  ]
}

// 模拟应用数据
const apps = ref([
  {
    id: 1,
    name: '示例应用1',
    description: '这是一个示例应用的描述信息',
    platform: 'ANDROID',
    category: '工具',
    latestVersion: '1.2.3',
    downloadCount: 1234,
    status: 'ACTIVE',
    iconUrl: '',
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-20 14:20:00'
  },
  {
    id: 2,
    name: '示例应用2',
    description: '另一个示例应用的描述',
    platform: 'IOS',
    category: '娱乐',
    latestVersion: '2.1.0',
    downloadCount: 567,
    status: 'ACTIVE',
    iconUrl: '',
    createTime: '2024-01-10 09:15:00',
    updateTime: '2024-01-25 16:45:00'
  }
])

// 计算属性
const filteredApps = computed(() => {
  let result = apps.value

  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(app => 
      app.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      app.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // 平台过滤
  if (platformFilter.value) {
    result = result.filter(app => app.platform === platformFilter.value)
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(app => app.status === statusFilter.value)
  }

  return result
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchQuery.value = ''
  platformFilter.value = ''
  statusFilter.value = ''
  currentPage.value = 1
}

const refreshData = () => {
  loading.value = true
  // 模拟加载
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

const handleSelectionChange = (selection) => {
  selectedApps.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const getPlatformType = (platform) => {
  const types = {
    'ANDROID': 'success',
    'IOS': 'primary',
    'WINDOWS': 'info',
    'MACOS': 'warning'
  }
  return types[platform] || 'info'
}

const getPlatformLabel = (platform) => {
  const labels = {
    'ANDROID': 'Android',
    'IOS': 'iOS',
    'WINDOWS': 'Windows',
    'MACOS': 'macOS'
  }
  return labels[platform] || platform
}

const formatNumber = (num) => {
  return num.toLocaleString()
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

const showAddDialog = () => {
  dialogType.value = 'add'
  resetAppForm()
  dialogVisible.value = true
}

const editApp = (app) => {
  dialogType.value = 'edit'
  Object.assign(appForm, app)
  dialogVisible.value = true
}

const viewApp = (app) => {
  selectedAppDetail.value = app
  detailDialogVisible.value = true
}

const deleteApp = async (app) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除应用 "${app.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟删除
    const index = apps.value.findIndex(item => item.id === app.id)
    if (index > -1) {
      apps.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消
  }
}

const resetAppForm = () => {
  Object.assign(appForm, {
    name: '',
    description: '',
    platform: '',
    category: '',
    iconUrl: '',
    status: 'ACTIVE'
  })
}

const submitApp = async () => {
  if (!appFormRef.value) return
  
  try {
    await appFormRef.value.validate()
    submitting.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (dialogType.value === 'add') {
      const newApp = {
        id: Date.now(),
        ...appForm,
        downloadCount: 0,
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString()
      }
      apps.value.unshift(newApp)
      ElMessage.success('添加成功')
    } else {
      const index = apps.value.findIndex(item => item.id === appForm.id)
      if (index > -1) {
        apps.value[index] = { ...apps.value[index], ...appForm, updateTime: new Date().toLocaleString() }
        ElMessage.success('更新成功')
      }
    }
    
    dialogVisible.value = false
  } catch (error) {
    console.error('提交错误:', error)
  } finally {
    submitting.value = false
  }
}

const handleIconSuccess = (response) => {
  appForm.iconUrl = response.url
  ElMessage.success('图标上传成功')
}

const beforeIconUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const batchEnable = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要启用选中的 ${selectedApps.value.length} 个应用吗？`,
      '批量操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    selectedApps.value.forEach(app => {
      const index = apps.value.findIndex(item => item.id === app.id)
      if (index > -1) {
        apps.value[index].status = 'ACTIVE'
      }
    })
    
    ElMessage.success('批量启用成功')
  } catch {
    // 用户取消
  }
}

const batchDisable = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要停用选中的 ${selectedApps.value.length} 个应用吗？`,
      '批量操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    selectedApps.value.forEach(app => {
      const index = apps.value.findIndex(item => item.id === app.id)
      if (index > -1) {
        apps.value[index].status = 'INACTIVE'
      }
    })
    
    ElMessage.success('批量停用成功')
  } catch {
    // 用户取消
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedApps.value.length} 个应用吗？此操作不可恢复！`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedApps.value.map(app => app.id)
    apps.value = apps.value.filter(app => !ids.includes(app.id))
    
    ElMessage.success('批量删除成功')
  } catch {
    // 用户取消
  }
}

// 生命周期
onMounted(() => {
  totalApps.value = apps.value.length
})
</script>

<style scoped lang="scss">
.app-management-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .page-description {
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

.filter-card {
  margin-bottom: 20px;
  
  .filter-actions {
    text-align: right;
  }
}

.table-card {
  .app-info {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .app-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
    }
    
    .icon-placeholder {
      width: 48px;
      height: 48px;
      background: #f0f2f5;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: #909399;
    }
    
    .app-details {
      flex: 1;
      
      .app-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .app-description {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .app-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .version {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  
  .batch-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    
    .batch-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

.icon-uploader {
  .uploaded-icon {
    width: 80px;
    height: 80px;
    border-radius: 8px;
  }
  
  .uploader-icon {
    width: 80px;
    height: 80px;
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #8c939d;
    cursor: pointer;
    
    &:hover {
      border-color: #409eff;
      color: #409eff;
    }
  }
}

.app-detail {
  .detail-header {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    
    .detail-icon {
      width: 100px;
      height: 100px;
      border-radius: 16px;
    }
    
    .detail-info {
      flex: 1;
      
      h2 {
        margin-bottom: 8px;
        color: #303133;
      }
      
      p {
        color: #606266;
        margin-bottom: 12px;
      }
      
      .detail-meta {
        display: flex;
        gap: 12px;
        align-items: center;
        
        .category {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-card {
    .el-row {
      .el-col {
        margin-bottom: 12px;
      }
    }
    
    .filter-actions {
      text-align: center;
    }
  }
  
  .batch-actions {
    position: static;
    transform: none;
    margin-top: 20px;
    
    .batch-content {
      flex-direction: column;
      gap: 12px;
    }
  }
}
</style> 