<template>
  <div class="dashboard-page">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon apps">
              <el-icon><App /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalApps }}</div>
              <div class="stat-label">应用总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon versions">
              <el-icon><Files /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalVersions }}</div>
              <div class="stat-label">版本总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon downloads">
              <el-icon><Download /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalDownloads }}</div>
              <div class="stat-label">下载总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon users">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalUsers }}</div>
              <div class="stat-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表和活动 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :lg="16">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>下载趋势</span>
              <el-select v-model="chartPeriod" size="small" style="width: 120px">
                <el-option label="最近7天" value="7" />
                <el-option label="最近30天" value="30" />
                <el-option label="最近90天" value="90" />
              </el-select>
            </div>
          </template>
          
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon"><TrendCharts /></el-icon>
              <p>下载趋势图表</p>
              <small>图表功能开发中...</small>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="8">
        <el-card class="activity-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
              <el-button type="text" size="small">查看全部</el-button>
            </div>
          </template>
          
          <div class="activity-list">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon" :class="activity.type">
                <el-icon>
                  <component :is="activity.icon" />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="actions-row">
      <el-col :xs="24" :lg="12">
        <el-card class="quick-actions-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>
          
          <div class="quick-actions">
            <el-button 
              type="primary" 
              icon="Plus"
              @click="goToAppManagement"
            >
              添加应用
            </el-button>
            <el-button 
              type="success" 
              icon="Upload"
              @click="goToVersionManagement"
            >
              上传版本
            </el-button>
            <el-button 
              type="info" 
              icon="Setting"
              @click="goToSettings"
            >
              系统设置
            </el-button>
            <el-button 
              type="warning" 
              icon="Document"
              @click="goToLogs"
            >
              查看日志
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card class="system-info-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
            </div>
          </template>
          
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">系统版本：</span>
              <span class="info-value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">运行时间：</span>
              <span class="info-value">{{ systemInfo.uptime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">服务器状态：</span>
              <el-tag type="success" size="small">正常</el-tag>
            </div>
            <div class="info-item">
              <span class="info-label">最后更新：</span>
              <span class="info-value">{{ systemInfo.lastUpdate }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  App, 
  Files, 
  Download, 
  User, 
  TrendCharts,
  Plus,
  Upload,
  Setting,
  Document
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const chartPeriod = ref('7')

// 统计数据
const stats = ref({
  totalApps: 25,
  totalVersions: 156,
  totalDownloads: 1234,
  totalUsers: 3
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    type: 'upload',
    icon: 'Upload',
    title: '上传了新版本 v2.1.0',
    time: '2分钟前'
  },
  {
    id: 2,
    type: 'download',
    icon: 'Download',
    title: '应用下载量突破1000',
    time: '10分钟前'
  },
  {
    id: 3,
    type: 'create',
    icon: 'Plus',
    title: '创建了新应用',
    time: '1小时前'
  },
  {
    id: 4,
    type: 'update',
    icon: 'Edit',
    title: '更新了应用信息',
    time: '2小时前'
  }
])

// 系统信息
const systemInfo = ref({
  uptime: '15天 8小时 32分钟',
  lastUpdate: '2024-01-27 14:30:00'
})

// 方法
const goToAppManagement = () => {
  router.push('/admin/apps')
}

const goToVersionManagement = () => {
  router.push('/admin/versions')
}

const goToSettings = () => {
  ElMessage('系统设置功能开发中...')
}

const goToLogs = () => {
  ElMessage('系统日志功能开发中...')
}

// 生命周期
onMounted(() => {
  // 可以在这里加载真实数据
})
</script>

<style scoped lang="scss">
.dashboard-page {
  .stats-row {
    margin-bottom: 20px;
  }
  
  .charts-row {
    margin-bottom: 20px;
  }
  
  .actions-row {
    margin-bottom: 20px;
  }
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      
      &.apps {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.versions {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.downloads {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.users {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
    
    .stat-info {
      flex: 1;
      
      .stat-number {
        font-size: 28px;
        font-weight: 700;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.chart-card {
  .chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .chart-placeholder {
      text-align: center;
      color: #909399;
      
      .chart-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      p {
        margin-bottom: 8px;
        font-size: 16px;
      }
      
      small {
        font-size: 12px;
      }
    }
  }
}

.activity-card {
  .activity-list {
    .activity-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f0f2f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .activity-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: white;
        
        &.upload {
          background: #67c23a;
        }
        
        &.download {
          background: #409eff;
        }
        
        &.create {
          background: #e6a23c;
        }
        
        &.update {
          background: #909399;
        }
      }
      
      .activity-content {
        flex: 1;
        
        .activity-title {
          font-size: 14px;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .activity-time {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

.quick-actions-card {
  .quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    
    .el-button {
      flex: 1;
      min-width: 120px;
    }
  }
}

.system-info-card {
  .system-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f2f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .info-label {
        font-size: 14px;
        color: #606266;
      }
      
      .info-value {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

// 响应式设计
@media (max-width: 768px) {
  .stat-card {
    .stat-content {
      .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
      }
      
      .stat-info {
        .stat-number {
          font-size: 24px;
        }
      }
    }
  }
  
  .quick-actions-card {
    .quick-actions {
      .el-button {
        min-width: 100px;
      }
    }
  }
}
</style> 