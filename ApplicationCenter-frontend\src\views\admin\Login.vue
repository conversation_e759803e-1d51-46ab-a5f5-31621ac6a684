<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <div class="logo">
          <el-icon class="logo-icon"><Setting /></el-icon>
        </div>
        <h1 class="login-title">管理员登录</h1>
        <p class="login-subtitle">APP版本管理系统</p>
      </div>
      
      <el-form 
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="rememberMe">记住我</el-checkbox>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <el-button type="text" @click="goToHome">返回首页</el-button>
        <el-button type="text" @click="showHelp">帮助</el-button>
      </div>
    </div>

    <!-- 帮助对话框 -->
    <el-dialog
      v-model="helpDialogVisible"
      title="登录帮助"
      width="500px"
    >
      <div class="help-content">
        <h3>默认管理员账户</h3>
        <p><strong>用户名：</strong>admin</p>
        <p><strong>密码：</strong>admin123</p>
        
        <el-divider />
        
        <h3>注意事项</h3>
        <ul>
          <li>请使用管理员分配的账户登录</li>
          <li>密码区分大小写</li>
          <li>如忘记密码请联系系统管理员</li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Setting, User, Lock } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import request from '@/utils/request'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loginFormRef = ref()
const loading = ref(false)
const rememberMe = ref(false)
const helpDialogVisible = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 方法
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    // 模拟登录请求
    const response = await mockLogin(loginForm.username, loginForm.password)
    
    if (response.success) {
      // 保存登录状态
      userStore.setToken(response.token)
      userStore.setUsername(loginForm.username)
      
      ElMessage.success('登录成功')
      
      // 跳转到管理员后台
      router.push('/admin')
    } else {
      ElMessage.error(response.message || '登录失败')
    }
  } catch (error) {
    console.error('登录错误:', error)
    ElMessage.error('登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}

// 模拟登录API
const mockLogin = async (username, password) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 模拟验证
  if (username === 'admin' && password === 'admin123') {
    return {
      success: true,
      token: 'mock_jwt_token_' + Date.now(),
      message: '登录成功'
    }
  } else {
    return {
      success: false,
      message: '用户名或密码错误'
    }
  }
}

const goToHome = () => {
  router.push('/')
}

const showHelp = () => {
  helpDialogVisible.value = true
}
</script>

<style scoped lang="scss">
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  
  .logo {
    margin-bottom: 20px;
    
    .logo-icon {
      font-size: 48px;
      color: #409eff;
    }
  }
  
  .login-title {
    font-size: 28px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }
  
  .login-subtitle {
    font-size: 16px;
    color: #909399;
  }
}

.login-form {
  .login-button {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
  }
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
}

.help-content {
  h3 {
    color: #303133;
    margin-bottom: 12px;
  }
  
  p {
    color: #606266;
    margin-bottom: 8px;
    
    strong {
      color: #303133;
    }
  }
  
  ul {
    color: #606266;
    padding-left: 20px;
    
    li {
      margin-bottom: 6px;
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-container {
    padding: 30px 20px;
  }
  
  .login-header {
    .login-title {
      font-size: 24px;
    }
    
    .login-subtitle {
      font-size: 14px;
    }
  }
  
  .login-footer {
    flex-direction: column;
    gap: 10px;
  }
}
</style> 