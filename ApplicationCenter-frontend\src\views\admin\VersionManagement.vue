<template>
  <div class="version-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">版本管理</h1>
        <p class="page-description">管理应用的版本信息和文件</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="Upload" @click="showUploadDialog">
          上传版本
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card" shadow="never">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="appFilter" placeholder="选择应用" clearable @change="handleFilter">
            <el-option label="全部应用" value="" />
            <el-option
              v-for="app in apps"
              :key="app.id"
              :label="app.name"
              :value="app.id"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="platformFilter" placeholder="平台" clearable @change="handleFilter">
            <el-option label="全部平台" value="" />
            <el-option label="Android" value="ANDROID" />
            <el-option label="iOS" value="IOS" />
            <el-option label="Windows" value="WINDOWS" />
            <el-option label="macOS" value="MACOS" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态" clearable @change="handleFilter">
            <el-option label="全部状态" value="" />
            <el-option label="发布" value="PUBLISHED" />
            <el-option label="测试" value="TESTING" />
            <el-option label="草稿" value="DRAFT" />
          </el-select>
        </el-col>
        <el-col :span="10">
          <div class="filter-actions">
            <el-button @click="resetFilters">重置</el-button>
            <el-button type="primary" @click="refreshData">刷新</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 版本列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="filteredVersions"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="版本信息" min-width="300">
          <template #default="{ row }">
            <div class="version-info">
              <div class="app-icon">
                <el-image
                  :src="row.appIconUrl || '/default-app-icon.png'"
                  fit="cover"
                >
                  <template #error>
                    <div class="icon-placeholder">
                      <el-icon><App /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              <div class="version-details">
                <div class="app-name">{{ row.appName }}</div>
                <div class="version-number">v{{ row.versionNumber }}</div>
                <div class="version-meta">
                  <el-tag size="small" :type="getPlatformType(row.platform)">
                    {{ getPlatformLabel(row.platform) }}
                  </el-tag>
                  <span class="buildNumber">Build {{ row.buildNumber }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="更新说明" min-width="200">
          <template #default="{ row }">
            <div class="description-text">
              {{ row.description }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="fileSize" label="文件大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="downloadCount" label="下载量" width="100" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.downloadCount) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="releaseTime" label="发布时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatDate(row.releaseTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewVersion(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editVersion(row)">编辑</el-button>
            <el-button size="small" type="success" @click="downloadVersion(row)">下载</el-button>
            <el-button size="small" type="danger" @click="deleteVersion(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalVersions"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量操作 -->
    <div v-if="selectedVersions.length > 0" class="batch-actions">
      <el-card shadow="never">
        <div class="batch-content">
          <span>已选择 {{ selectedVersions.length }} 个版本</span>
          <div class="batch-buttons">
            <el-button size="small" @click="batchPublish">批量发布</el-button>
            <el-button size="small" type="warning" @click="batchUnpublish">批量下架</el-button>
            <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 上传版本对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传版本"
      width="700px"
    >
      <el-form
        ref="uploadFormRef"
        :model="uploadForm"
        :rules="uploadRules"
        label-width="100px"
      >
        <el-form-item label="选择应用" prop="appId">
          <el-select v-model="uploadForm.appId" placeholder="请选择应用">
            <el-option
              v-for="app in apps"
              :key="app.id"
              :label="app.name"
              :value="app.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="版本号" prop="versionNumber">
          <el-input v-model="uploadForm.versionNumber" placeholder="例如：1.2.3" />
        </el-form-item>
        
        <el-form-item label="构建号" prop="buildNumber">
          <el-input v-model="uploadForm.buildNumber" placeholder="例如：123" />
        </el-form-item>
        
        <el-form-item label="更新说明" prop="description">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入版本更新说明"
          />
        </el-form-item>
        
        <el-form-item label="版本文件" prop="file">
          <el-upload
            class="version-uploader"
            action="/api/upload"
            :auto-upload="false"
            :on-change="handleFileChange"
            :before-upload="beforeFileUpload"
            :file-list="fileList"
          >
            <el-button type="primary" icon="Upload">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 APK、IPA、EXE、DMG 格式，文件大小不超过 500MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="uploadForm.status">
            <el-radio label="DRAFT">草稿</el-radio>
            <el-radio label="TESTING">测试</el-radio>
            <el-radio label="PUBLISHED">发布</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            上传
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 版本详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="版本详情"
      width="800px"
    >
      <div v-if="selectedVersionDetail" class="version-detail">
        <div class="detail-header">
          <el-image
            :src="selectedVersionDetail.appIconUrl || '/default-app-icon.png'"
            class="detail-icon"
            fit="cover"
          />
          <div class="detail-info">
            <h2>{{ selectedVersionDetail.appName }}</h2>
            <div class="version-number">v{{ selectedVersionDetail.versionNumber }}</div>
            <div class="detail-meta">
              <el-tag :type="getPlatformType(selectedVersionDetail.platform)">
                {{ getPlatformLabel(selectedVersionDetail.platform) }}
              </el-tag>
              <el-tag :type="getStatusType(selectedVersionDetail.status)">
                {{ getStatusLabel(selectedVersionDetail.status) }}
              </el-tag>
              <span class="buildNumber">Build {{ selectedVersionDetail.buildNumber }}</span>
            </div>
          </div>
        </div>
        
        <el-divider />
        
        <div class="detail-content">
          <h3>更新说明</h3>
          <p>{{ selectedVersionDetail.description }}</p>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件大小">
              {{ formatFileSize(selectedVersionDetail.fileSize) }}
            </el-descriptions-item>
            <el-descriptions-item label="下载量">
              {{ formatNumber(selectedVersionDetail.downloadCount) }}
            </el-descriptions-item>
            <el-descriptions-item label="发布时间">
              {{ formatDate(selectedVersionDetail.releaseTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="文件哈希">
              {{ selectedVersionDetail.fileHash }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, App } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const appFilter = ref('')
const platformFilter = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalVersions = ref(0)
const selectedVersions = ref([])
const uploadDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const selectedVersionDetail = ref(null)
const uploading = ref(false)
const fileList = ref([])

const uploadFormRef = ref()

// 表单数据
const uploadForm = reactive({
  appId: '',
  versionNumber: '',
  buildNumber: '',
  description: '',
  status: 'DRAFT'
})

// 表单验证规则
const uploadRules = {
  appId: [
    { required: true, message: '请选择应用', trigger: 'change' }
  ],
  versionNumber: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，如：1.2.3', trigger: 'blur' }
  ],
  buildNumber: [
    { required: true, message: '请输入构建号', trigger: 'blur' },
    { pattern: /^\d+$/, message: '构建号必须为数字', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入更新说明', trigger: 'blur' }
  ]
}

// 模拟应用数据
const apps = ref([
  { id: 1, name: '示例应用1', platform: 'ANDROID' },
  { id: 2, name: '示例应用2', platform: 'IOS' }
])

// 模拟版本数据
const versions = ref([
  {
    id: 1,
    appId: 1,
    appName: '示例应用1',
    appIconUrl: '',
    platform: 'ANDROID',
    versionNumber: '1.2.3',
    buildNumber: '123',
    description: '修复了一些bug，提升了性能',
    fileSize: 25600000, // 25MB
    downloadCount: 1234,
    status: 'PUBLISHED',
    releaseTime: '2024-01-20 14:30:00',
    fileHash: 'a1b2c3d4e5f6...'
  },
  {
    id: 2,
    appId: 2,
    appName: '示例应用2',
    appIconUrl: '',
    platform: 'IOS',
    versionNumber: '2.1.0',
    buildNumber: '456',
    description: '新增了重要功能，优化了用户体验',
    fileSize: 51200000, // 50MB
    downloadCount: 567,
    status: 'TESTING',
    releaseTime: '2024-01-25 10:15:00',
    fileHash: 'f6e5d4c3b2a1...'
  }
])

// 计算属性
const filteredVersions = computed(() => {
  let result = versions.value

  // 应用过滤
  if (appFilter.value) {
    result = result.filter(version => version.appId === appFilter.value)
  }

  // 平台过滤
  if (platformFilter.value) {
    result = result.filter(version => version.platform === platformFilter.value)
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(version => version.status === statusFilter.value)
  }

  return result
})

// 方法
const handleFilter = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  appFilter.value = ''
  platformFilter.value = ''
  statusFilter.value = ''
  currentPage.value = 1
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

const handleSelectionChange = (selection) => {
  selectedVersions.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const getPlatformType = (platform) => {
  const types = {
    'ANDROID': 'success',
    'IOS': 'primary',
    'WINDOWS': 'info',
    'MACOS': 'warning'
  }
  return types[platform] || 'info'
}

const getPlatformLabel = (platform) => {
  const labels = {
    'ANDROID': 'Android',
    'IOS': 'iOS',
    'WINDOWS': 'Windows',
    'MACOS': 'macOS'
  }
  return labels[platform] || platform
}

const getStatusType = (status) => {
  const types = {
    'PUBLISHED': 'success',
    'TESTING': 'warning',
    'DRAFT': 'info'
  }
  return types[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    'PUBLISHED': '发布',
    'TESTING': '测试',
    'DRAFT': '草稿'
  }
  return labels[status] || status
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatNumber = (num) => {
  return num.toLocaleString()
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

const showUploadDialog = () => {
  uploadDialogVisible.value = true
  resetUploadForm()
}

const resetUploadForm = () => {
  Object.assign(uploadForm, {
    appId: '',
    versionNumber: '',
    buildNumber: '',
    description: '',
    status: 'DRAFT'
  })
  fileList.value = []
}

const handleFileChange = (file) => {
  fileList.value = [file]
}

const beforeFileUpload = (file) => {
  const allowedTypes = [
    'application/vnd.android.package-archive', // APK
    'application/octet-stream', // IPA, EXE, DMG
    'application/x-msdownload' // EXE
  ]
  const isAllowedType = allowedTypes.includes(file.type) || 
    file.name.endsWith('.apk') || 
    file.name.endsWith('.ipa') || 
    file.name.endsWith('.exe') || 
    file.name.endsWith('.dmg')
  const isLt500M = file.size / 1024 / 1024 < 500

  if (!isAllowedType) {
    ElMessage.error('只支持 APK、IPA、EXE、DMG 格式的文件!')
    return false
  }
  if (!isLt500M) {
    ElMessage.error('文件大小不能超过 500MB!')
    return false
  }
  return true
}

const submitUpload = async () => {
  if (!uploadFormRef.value) return
  
  try {
    await uploadFormRef.value.validate()
    uploading.value = true
    
    // 模拟上传
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const newVersion = {
      id: Date.now(),
      appId: uploadForm.appId,
      appName: apps.value.find(app => app.id === uploadForm.appId)?.name || '',
      appIconUrl: '',
      platform: apps.value.find(app => app.id === uploadForm.appId)?.platform || '',
      versionNumber: uploadForm.versionNumber,
      buildNumber: uploadForm.buildNumber,
      description: uploadForm.description,
      fileSize: fileList.value[0]?.size || 0,
      downloadCount: 0,
      status: uploadForm.status,
      releaseTime: new Date().toLocaleString(),
      fileHash: 'mock_hash_' + Date.now()
    }
    
    versions.value.unshift(newVersion)
    ElMessage.success('版本上传成功')
    uploadDialogVisible.value = false
  } catch (error) {
    console.error('上传错误:', error)
  } finally {
    uploading.value = false
  }
}

const viewVersion = (version) => {
  selectedVersionDetail.value = version
  detailDialogVisible.value = true
}

const editVersion = (version) => {
  ElMessage('编辑功能开发中...')
}

const downloadVersion = (version) => {
  ElMessage.success(`开始下载 ${version.appName} v${version.versionNumber}`)
}

const deleteVersion = async (version) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除版本 "${version.appName} v${version.versionNumber}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = versions.value.findIndex(item => item.id === version.id)
    if (index > -1) {
      versions.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消
  }
}

const batchPublish = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要发布选中的 ${selectedVersions.value.length} 个版本吗？`,
      '批量操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    selectedVersions.value.forEach(version => {
      const index = versions.value.findIndex(item => item.id === version.id)
      if (index > -1) {
        versions.value[index].status = 'PUBLISHED'
      }
    })
    
    ElMessage.success('批量发布成功')
  } catch {
    // 用户取消
  }
}

const batchUnpublish = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要下架选中的 ${selectedVersions.value.length} 个版本吗？`,
      '批量操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    selectedVersions.value.forEach(version => {
      const index = versions.value.findIndex(item => item.id === version.id)
      if (index > -1) {
        versions.value[index].status = 'DRAFT'
      }
    })
    
    ElMessage.success('批量下架成功')
  } catch {
    // 用户取消
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedVersions.value.length} 个版本吗？此操作不可恢复！`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedVersions.value.map(version => version.id)
    versions.value = versions.value.filter(version => !ids.includes(version.id))
    
    ElMessage.success('批量删除成功')
  } catch {
    // 用户取消
  }
}

// 生命周期
onMounted(() => {
  totalVersions.value = versions.value.length
})
</script>

<style scoped lang="scss">
.version-management-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .page-description {
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

.filter-card {
  margin-bottom: 20px;
  
  .filter-actions {
    text-align: right;
  }
}

.table-card {
  .version-info {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .app-icon {
      width: 48px;
      height: 48px;
      
      .el-image {
        width: 100%;
        height: 100%;
        border-radius: 8px;
      }
      
      .icon-placeholder {
        width: 48px;
        height: 48px;
        background: #f0f2f5;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: #909399;
      }
    }
    
    .version-details {
      flex: 1;
      
      .app-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .version-number {
        font-size: 14px;
        color: #409eff;
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .version-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .buildNumber {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .description-text {
    font-size: 14px;
    color: #606266;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  
  .batch-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    
    .batch-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

.version-uploader {
  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
  }
}

.version-detail {
  .detail-header {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    
    .detail-icon {
      width: 100px;
      height: 100px;
      border-radius: 16px;
    }
    
    .detail-info {
      flex: 1;
      
      h2 {
        margin-bottom: 8px;
        color: #303133;
      }
      
      .version-number {
        font-size: 18px;
        color: #409eff;
        font-weight: 600;
        margin-bottom: 12px;
      }
      
      .detail-meta {
        display: flex;
        gap: 12px;
        align-items: center;
        
        .buildNumber {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }
  
  .detail-content {
    h3 {
      margin-bottom: 12px;
      color: #303133;
    }
    
    p {
      color: #606266;
      line-height: 1.6;
      margin-bottom: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-card {
    .el-row {
      .el-col {
        margin-bottom: 12px;
      }
    }
    
    .filter-actions {
      text-align: center;
    }
  }
  
  .batch-actions {
    position: static;
    transform: none;
    margin-top: 20px;
    
    .batch-content {
      flex-direction: column;
      gap: 12px;
    }
  }
}
</style> 