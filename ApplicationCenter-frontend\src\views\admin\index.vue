<template>
  <div class="admin-layout">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-left">
        <div class="logo">
          <el-icon class="logo-icon"><Setting /></el-icon>
          <span class="logo-text">APP版本管理系统</span>
        </div>
      </div>
      
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <div class="user-info">
            <el-avatar :size="32" icon="User" />
            <span class="username">{{ userStore.username || '管理员' }}</span>
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="settings">系统设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 侧边栏 -->
      <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
        <el-menu
          :default-active="activeMenu"
          :collapse="sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/admin">
            <el-icon><DataBoard /></el-icon>
            <template #title>仪表盘</template>
          </el-menu-item>
          
          <el-menu-item index="/admin/apps">
            <el-icon><App /></el-icon>
            <template #title>应用管理</template>
          </el-menu-item>
          
          <el-menu-item index="/admin/versions">
            <el-icon><Files /></el-icon>
            <template #title>版本管理</template>
          </el-menu-item>
          
          <el-sub-menu index="statistics">
            <template #title>
              <el-icon><TrendCharts /></el-icon>
              <span>统计分析</span>
            </template>
            <el-menu-item index="/admin/statistics/downloads">下载统计</el-menu-item>
            <el-menu-item index="/admin/statistics/users">用户统计</el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="system">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/admin/system/users">用户管理</el-menu-item>
            <el-menu-item index="/admin/system/logs">系统日志</el-menu-item>
          </el-sub-menu>
        </el-menu>
        
        <!-- 折叠按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon>
            <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
          </el-icon>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/admin' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="currentPageTitle">{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <!-- 路由视图 -->
        <div class="page-content">
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { 
  Setting, 
  ArrowDown, 
  DataBoard, 
  App, 
  Files, 
  TrendCharts,
  Expand,
  Fold
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const sidebarCollapsed = ref(false)

// 计算属性
const activeMenu = computed(() => route.path)

const currentPageTitle = computed(() => {
  const routeMap = {
    '/admin': '仪表盘',
    '/admin/apps': '应用管理',
    '/admin/versions': '版本管理',
    '/admin/statistics/downloads': '下载统计',
    '/admin/statistics/users': '用户统计',
    '/admin/system/users': '用户管理',
    '/admin/system/logs': '系统日志'
  }
  return routeMap[route.path] || ''
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage('个人资料功能开发中...')
      break
    case 'settings':
      ElMessage('系统设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        userStore.logout()
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style scoped lang="scss">
.admin-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 1000;
  
  .header-left {
    .logo {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .logo-icon {
        font-size: 24px;
        color: #409eff;
      }
      
      .logo-text {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }
  }
  
  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      .username {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 240px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  transition: width 0.3s;
  
  &.collapsed {
    width: 64px;
  }
  
  .sidebar-menu {
    flex: 1;
    border-right: none;
  }
  
  .sidebar-toggle {
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #e4e7ed;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    .el-icon {
      font-size: 16px;
      color: #909399;
    }
  }
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  transition: margin-left 0.3s;
  
  &.sidebar-collapsed {
    margin-left: 0;
  }
  
  .breadcrumb {
    padding: 16px 20px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
  }
  
  .page-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    .header-left {
      .logo-text {
        display: none;
      }
    }
  }
  
  .sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s;
    
    &.collapsed {
      transform: translateX(0);
    }
  }
  
  .content {
    margin-left: 0;
    
    .page-content {
      padding: 16px;
    }
  }
}
</style> 