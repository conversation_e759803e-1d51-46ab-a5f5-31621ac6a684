<template>
  <div class="downloads-statistics-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">下载统计</h1>
        <p class="page-description">分析应用下载数据和趋势</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="Download" @click="exportData">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Download /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalDownloads }}</div>
              <div class="stat-label">总下载量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon today">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.todayDownloads }}</div>
              <div class="stat-label">今日下载</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon week">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.weekDownloads }}</div>
              <div class="stat-label">本周下载</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon month">
              <el-icon><DataLine /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.monthDownloads }}</div>
              <div class="stat-label">本月下载</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选器 -->
    <el-card class="filter-card" shadow="never">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="appFilter" placeholder="选择应用" clearable @change="handleFilter">
            <el-option label="全部应用" value="" />
            <el-option
              v-for="app in apps"
              :key="app.id"
              :label="app.name"
              :value="app.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="platformFilter" placeholder="选择平台" clearable @change="handleFilter">
            <el-option label="全部平台" value="" />
            <el-option label="Android" value="ANDROID" />
            <el-option label="iOS" value="IOS" />
            <el-option label="Windows" value="WINDOWS" />
            <el-option label="macOS" value="MACOS" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-col>
        <el-col :span="6">
          <div class="filter-actions">
            <el-button @click="resetFilters">重置</el-button>
            <el-button type="primary" @click="refreshData">刷新</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :lg="16">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>下载趋势</span>
              <el-select v-model="trendPeriod" size="small" style="width: 120px">
                <el-option label="最近7天" value="7" />
                <el-option label="最近30天" value="30" />
                <el-option label="最近90天" value="90" />
              </el-select>
            </div>
          </template>
          
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon"><TrendCharts /></el-icon>
              <p>下载趋势图表</p>
              <small>图表功能开发中...</small>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="8">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>平台分布</span>
            </div>
          </template>
          
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon"><PieChart /></el-icon>
              <p>平台分布图表</p>
              <small>图表功能开发中...</small>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 应用排行 -->
    <el-row :gutter="20" class="ranking-row">
      <el-col :xs="24" :lg="12">
        <el-card class="ranking-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>应用下载排行</span>
              <el-button type="text" size="small" @click="viewAllApps">查看全部</el-button>
            </div>
          </template>
          
          <div class="ranking-list">
            <div
              v-for="(app, index) in topApps"
              :key="app.id"
              class="ranking-item"
            >
              <div class="ranking-number" :class="getRankingClass(index + 1)">
                {{ index + 1 }}
              </div>
              <div class="app-info">
                <div class="app-name">{{ app.name }}</div>
                <div class="app-platform">{{ getPlatformLabel(app.platform) }}</div>
              </div>
              <div class="download-count">
                {{ formatNumber(app.downloadCount) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card class="ranking-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>版本下载排行</span>
              <el-button type="text" size="small" @click="viewAllVersions">查看全部</el-button>
            </div>
          </template>
          
          <div class="ranking-list">
            <div
              v-for="(version, index) in topVersions"
              :key="version.id"
              class="ranking-item"
            >
              <div class="ranking-number" :class="getRankingClass(index + 1)">
                {{ index + 1 }}
              </div>
              <div class="version-info">
                <div class="version-name">{{ version.appName }}</div>
                <div class="version-number">v{{ version.versionNumber }}</div>
              </div>
              <div class="download-count">
                {{ formatNumber(version.downloadCount) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>下载记录</span>
          <el-button type="text" size="small" @click="viewAllRecords">查看全部</el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="downloadRecords"
        style="width: 100%"
        max-height="400"
      >
        <el-table-column label="应用" min-width="200">
          <template #default="{ row }">
            <div class="app-info-cell">
              <el-image
                :src="row.appIconUrl || '/default-app-icon.png'"
                class="app-icon"
                fit="cover"
              />
              <div class="app-details">
                <div class="app-name">{{ row.appName }}</div>
                <div class="version-number">v{{ row.versionNumber }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="platform" label="平台" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="getPlatformType(row.platform)">
              {{ getPlatformLabel(row.platform) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="downloadTime" label="下载时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatDate(row.downloadTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="userAgent" label="用户代理" min-width="200">
          <template #default="{ row }">
            <el-tooltip :content="row.userAgent" placement="top">
              <span class="user-agent">{{ truncateText(row.userAgent, 50) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column prop="ipAddress" label="IP地址" width="120" />
        
        <el-table-column prop="location" label="地区" width="100" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Download, 
  Calendar, 
  TrendCharts, 
  DataLine,
  PieChart
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const appFilter = ref('')
const platformFilter = ref('')
const dateRange = ref([])
const trendPeriod = ref('7')

// 统计数据
const stats = ref({
  totalDownloads: 12345,
  todayDownloads: 234,
  weekDownloads: 1234,
  monthDownloads: 5678
})

// 模拟应用数据
const apps = ref([
  { id: 1, name: '示例应用1', platform: 'ANDROID' },
  { id: 2, name: '示例应用2', platform: 'IOS' },
  { id: 3, name: '示例应用3', platform: 'WINDOWS' }
])

// 应用排行数据
const topApps = ref([
  { id: 1, name: '示例应用1', platform: 'ANDROID', downloadCount: 3456 },
  { id: 2, name: '示例应用2', platform: 'IOS', downloadCount: 2345 },
  { id: 3, name: '示例应用3', platform: 'WINDOWS', downloadCount: 1234 },
  { id: 4, name: '示例应用4', platform: 'MACOS', downloadCount: 987 },
  { id: 5, name: '示例应用5', platform: 'ANDROID', downloadCount: 876 }
])

// 版本排行数据
const topVersions = ref([
  { id: 1, appName: '示例应用1', versionNumber: '2.1.0', downloadCount: 1234 },
  { id: 2, appName: '示例应用2', versionNumber: '1.5.2', downloadCount: 987 },
  { id: 3, appName: '示例应用3', versionNumber: '3.0.1', downloadCount: 876 },
  { id: 4, appName: '示例应用4', versionNumber: '1.2.3', downloadCount: 765 },
  { id: 5, appName: '示例应用5', versionNumber: '2.0.0', downloadCount: 654 }
])

// 下载记录数据
const downloadRecords = ref([
  {
    id: 1,
    appName: '示例应用1',
    versionNumber: '2.1.0',
    appIconUrl: '',
    platform: 'ANDROID',
    downloadTime: '2024-01-27 15:30:00',
    userAgent: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
    ipAddress: '*************',
    location: '北京'
  },
  {
    id: 2,
    appName: '示例应用2',
    versionNumber: '1.5.2',
    appIconUrl: '',
    platform: 'IOS',
    downloadTime: '2024-01-27 14:20:00',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
    ipAddress: '*************',
    location: '上海'
  }
])

// 方法
const handleFilter = () => {
  refreshData()
}

const handleDateChange = () => {
  refreshData()
}

const resetFilters = () => {
  appFilter.value = ''
  platformFilter.value = ''
  dateRange.value = []
  refreshData()
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

const getPlatformType = (platform) => {
  const types = {
    'ANDROID': 'success',
    'IOS': 'primary',
    'WINDOWS': 'info',
    'MACOS': 'warning'
  }
  return types[platform] || 'info'
}

const getPlatformLabel = (platform) => {
  const labels = {
    'ANDROID': 'Android',
    'IOS': 'iOS',
    'WINDOWS': 'Windows',
    'MACOS': 'macOS'
  }
  return labels[platform] || platform
}

const getRankingClass = (rank) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return 'bronze'
  return 'normal'
}

const formatNumber = (num) => {
  return num.toLocaleString()
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

const truncateText = (text, maxLength) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const exportData = () => {
  ElMessage.success('数据导出功能开发中...')
}

const viewAllApps = () => {
  ElMessage('查看全部应用功能开发中...')
}

const viewAllVersions = () => {
  ElMessage('查看全部版本功能开发中...')
}

const viewAllRecords = () => {
  ElMessage('查看全部记录功能开发中...')
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped lang="scss">
.downloads-statistics-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .page-description {
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .stats-row {
    margin-bottom: 20px;
  }
  
  .filter-card {
    margin-bottom: 20px;
    
    .filter-actions {
      text-align: right;
    }
  }
  
  .charts-row {
    margin-bottom: 20px;
  }
  
  .ranking-row {
    margin-bottom: 20px;
  }
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      
      &.total {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.today {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.week {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.month {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
    
    .stat-info {
      flex: 1;
      
      .stat-number {
        font-size: 28px;
        font-weight: 700;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.chart-card {
  .chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .chart-placeholder {
      text-align: center;
      color: #909399;
      
      .chart-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      p {
        margin-bottom: 8px;
        font-size: 16px;
      }
      
      small {
        font-size: 12px;
      }
    }
  }
}

.ranking-card {
  .ranking-list {
    .ranking-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f0f2f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .ranking-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
        color: white;
        
        &.gold {
          background: #f7ba2a;
        }
        
        &.silver {
          background: #c0c4cc;
        }
        
        &.bronze {
          background: #b87333;
        }
        
        &.normal {
          background: #909399;
        }
      }
      
      .app-info, .version-info {
        flex: 1;
        
        .app-name, .version-name {
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .app-platform, .version-number {
          font-size: 12px;
          color: #909399;
        }
      }
      
      .download-count {
        font-weight: 600;
        color: #409eff;
      }
    }
  }
}

.table-card {
  .app-info-cell {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .app-icon {
      width: 40px;
      height: 40px;
      border-radius: 6px;
    }
    
    .app-details {
      .app-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 2px;
      }
      
      .version-number {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .user-agent {
    font-family: monospace;
    font-size: 12px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-card {
    .el-row {
      .el-col {
        margin-bottom: 12px;
      }
    }
    
    .filter-actions {
      text-align: center;
    }
  }
  
  .stat-card {
    .stat-content {
      .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
      }
      
      .stat-info {
        .stat-number {
          font-size: 24px;
        }
      }
    }
  }
}
</style> 