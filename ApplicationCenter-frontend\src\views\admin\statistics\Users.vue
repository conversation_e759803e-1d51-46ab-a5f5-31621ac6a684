<template>
  <div class="users-statistics-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">用户统计</h1>
        <p class="page-description">分析用户活跃度和增长趋势</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="Download" @click="exportData">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalUsers }}</div>
              <div class="stat-label">总用户数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.activeUsers }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon new">
              <el-icon><UserPlus /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.newUsers }}</div>
              <div class="stat-label">新增用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon retention">
              <el-icon><DataLine /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.retentionRate }}%</div>
              <div class="stat-label">留存率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选器 -->
    <el-card class="filter-card" shadow="never">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="userTypeFilter" placeholder="用户类型" clearable @change="handleFilter">
            <el-option label="全部用户" value="" />
            <el-option label="普通用户" value="NORMAL" />
            <el-option label="VIP用户" value="VIP" />
            <el-option label="管理员" value="ADMIN" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="statusFilter" placeholder="用户状态" clearable @change="handleFilter">
            <el-option label="全部状态" value="" />
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="非活跃" value="INACTIVE" />
            <el-option label="禁用" value="DISABLED" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-col>
        <el-col :span="6">
          <div class="filter-actions">
            <el-button @click="resetFilters">重置</el-button>
            <el-button type="primary" @click="refreshData">刷新</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :lg="16">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>用户增长趋势</span>
              <el-select v-model="growthPeriod" size="small" style="width: 120px">
                <el-option label="最近7天" value="7" />
                <el-option label="最近30天" value="30" />
                <el-option label="最近90天" value="90" />
              </el-select>
            </div>
          </template>
          
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon"><TrendCharts /></el-icon>
              <p>用户增长趋势图表</p>
              <small>图表功能开发中...</small>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="8">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>用户类型分布</span>
            </div>
          </template>
          
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon"><PieChart /></el-icon>
              <p>用户类型分布图表</p>
              <small>图表功能开发中...</small>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户排行 -->
    <el-row :gutter="20" class="ranking-row">
      <el-col :xs="24" :lg="12">
        <el-card class="ranking-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>活跃用户排行</span>
              <el-button type="text" size="small" @click="viewAllUsers">查看全部</el-button>
            </div>
          </template>
          
          <div class="ranking-list">
            <div
              v-for="(user, index) in topUsers"
              :key="user.id"
              class="ranking-item"
            >
              <div class="ranking-number" :class="getRankingClass(index + 1)">
                {{ index + 1 }}
              </div>
              <div class="user-info">
                <el-avatar :size="40" :src="user.avatar" />
                <div class="user-details">
                  <div class="user-name">{{ user.username }}</div>
                  <div class="user-email">{{ user.email }}</div>
                </div>
              </div>
              <div class="user-stats">
                <div class="stat-value">{{ user.downloadCount }}</div>
                <div class="stat-label">下载次数</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card class="ranking-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>新增用户排行</span>
              <el-button type="text" size="small" @click="viewNewUsers">查看全部</el-button>
            </div>
          </template>
          
          <div class="ranking-list">
            <div
              v-for="(user, index) in newUsers"
              :key="user.id"
              class="ranking-item"
            >
              <div class="ranking-number" :class="getRankingClass(index + 1)">
                {{ index + 1 }}
              </div>
              <div class="user-info">
                <el-avatar :size="40" :src="user.avatar" />
                <div class="user-details">
                  <div class="user-name">{{ user.username }}</div>
                  <div class="user-email">{{ user.email }}</div>
                </div>
              </div>
              <div class="user-stats">
                <div class="stat-value">{{ formatDate(user.registerTime) }}</div>
                <div class="stat-label">注册时间</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户行为分析 -->
    <el-row :gutter="20" class="behavior-row">
      <el-col :xs="24" :lg="12">
        <el-card class="behavior-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>用户行为分析</span>
            </div>
          </template>
          
          <div class="behavior-list">
            <div class="behavior-item">
              <div class="behavior-icon">
                <el-icon><Download /></el-icon>
              </div>
              <div class="behavior-content">
                <div class="behavior-title">平均下载次数</div>
                <div class="behavior-value">{{ behaviorStats.avgDownloads }}</div>
              </div>
            </div>
            
            <div class="behavior-item">
              <div class="behavior-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="behavior-content">
                <div class="behavior-title">平均使用时长</div>
                <div class="behavior-value">{{ behaviorStats.avgUsageTime }}</div>
              </div>
            </div>
            
            <div class="behavior-item">
              <div class="behavior-icon">
                <el-icon><Refresh /></el-icon>
              </div>
              <div class="behavior-content">
                <div class="behavior-title">平均访问频率</div>
                <div class="behavior-value">{{ behaviorStats.avgVisitFrequency }}</div>
              </div>
            </div>
            
            <div class="behavior-item">
              <div class="behavior-icon">
                <el-icon><Location /></el-icon>
              </div>
              <div class="behavior-content">
                <div class="behavior-title">主要地区</div>
                <div class="behavior-value">{{ behaviorStats.topRegion }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card class="retention-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>用户留存分析</span>
            </div>
          </template>
          
          <div class="retention-list">
            <div class="retention-item">
              <div class="retention-period">次日留存</div>
              <div class="retention-rate">{{ retentionStats.day1 }}%</div>
              <el-progress :percentage="retentionStats.day1" :color="getRetentionColor(retentionStats.day1)" />
            </div>
            
            <div class="retention-item">
              <div class="retention-period">7日留存</div>
              <div class="retention-rate">{{ retentionStats.day7 }}%</div>
              <el-progress :percentage="retentionStats.day7" :color="getRetentionColor(retentionStats.day7)" />
            </div>
            
            <div class="retention-item">
              <div class="retention-period">30日留存</div>
              <div class="retention-rate">{{ retentionStats.day30 }}%</div>
              <el-progress :percentage="retentionStats.day30" :color="getRetentionColor(retentionStats.day30)" />
            </div>
            
            <div class="retention-item">
              <div class="retention-period">90日留存</div>
              <div class="retention-rate">{{ retentionStats.day90 }}%</div>
              <el-progress :percentage="retentionStats.day90" :color="getRetentionColor(retentionStats.day90)" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户列表 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <el-button type="text" size="small" @click="viewAllUserList">查看全部</el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%"
        max-height="400"
      >
        <el-table-column label="用户信息" min-width="200">
          <template #default="{ row }">
            <div class="user-info-cell">
              <el-avatar :size="40" :src="row.avatar" />
              <div class="user-details">
                <div class="user-name">{{ row.username }}</div>
                <div class="user-email">{{ row.email }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="userType" label="用户类型" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="getUserTypeType(row.userType)">
              {{ getUserTypeLabel(row.userType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="downloadCount" label="下载次数" width="100" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.downloadCount) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="lastLoginTime" label="最后登录" width="180" sortable>
          <template #default="{ row }">
            {{ formatDate(row.lastLoginTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="registerTime" label="注册时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatDate(row.registerTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewUser(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editUser(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  User, 
  UserFilled, 
  UserPlus, 
  DataLine,
  TrendCharts,
  PieChart,
  Download,
  Clock,
  Refresh,
  Location
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const userTypeFilter = ref('')
const statusFilter = ref('')
const dateRange = ref([])
const growthPeriod = ref('7')

// 统计数据
const stats = ref({
  totalUsers: 1234,
  activeUsers: 987,
  newUsers: 56,
  retentionRate: 78.5
})

// 用户排行数据
const topUsers = ref([
  { id: 1, username: 'user001', email: '<EMAIL>', avatar: '', downloadCount: 156 },
  { id: 2, username: 'user002', email: '<EMAIL>', avatar: '', downloadCount: 134 },
  { id: 3, username: 'user003', email: '<EMAIL>', avatar: '', downloadCount: 98 },
  { id: 4, username: 'user004', email: '<EMAIL>', avatar: '', downloadCount: 87 },
  { id: 5, username: 'user005', email: '<EMAIL>', avatar: '', downloadCount: 76 }
])

// 新增用户数据
const newUsers = ref([
  { id: 1, username: 'newuser001', email: '<EMAIL>', avatar: '', registerTime: '2024-01-27 10:30:00' },
  { id: 2, username: 'newuser002', email: '<EMAIL>', avatar: '', registerTime: '2024-01-27 09:15:00' },
  { id: 3, username: 'newuser003', email: '<EMAIL>', avatar: '', registerTime: '2024-01-27 08:45:00' },
  { id: 4, username: 'newuser004', email: '<EMAIL>', avatar: '', registerTime: '2024-01-26 16:20:00' },
  { id: 5, username: 'newuser005', email: '<EMAIL>', avatar: '', registerTime: '2024-01-26 14:30:00' }
])

// 用户行为统计
const behaviorStats = ref({
  avgDownloads: 12.5,
  avgUsageTime: '45分钟',
  avgVisitFrequency: '3.2次/周',
  topRegion: '北京'
})

// 留存率统计
const retentionStats = ref({
  day1: 85,
  day7: 65,
  day30: 45,
  day90: 25
})

// 用户列表数据
const userList = ref([
  {
    id: 1,
    username: 'user001',
    email: '<EMAIL>',
    avatar: '',
    userType: 'NORMAL',
    status: 'ACTIVE',
    downloadCount: 156,
    lastLoginTime: '2024-01-27 15:30:00',
    registerTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    username: 'vipuser001',
    email: '<EMAIL>',
    avatar: '',
    userType: 'VIP',
    status: 'ACTIVE',
    downloadCount: 234,
    lastLoginTime: '2024-01-27 14:20:00',
    registerTime: '2023-12-15 09:30:00'
  }
])

// 方法
const handleFilter = () => {
  refreshData()
}

const handleDateChange = () => {
  refreshData()
}

const resetFilters = () => {
  userTypeFilter.value = ''
  statusFilter.value = ''
  dateRange.value = []
  refreshData()
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

const getRankingClass = (rank) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return 'bronze'
  return 'normal'
}

const getUserTypeType = (userType) => {
  const types = {
    'NORMAL': 'info',
    'VIP': 'warning',
    'ADMIN': 'danger'
  }
  return types[userType] || 'info'
}

const getUserTypeLabel = (userType) => {
  const labels = {
    'NORMAL': '普通用户',
    'VIP': 'VIP用户',
    'ADMIN': '管理员'
  }
  return labels[userType] || userType
}

const getStatusType = (status) => {
  const types = {
    'ACTIVE': 'success',
    'INACTIVE': 'warning',
    'DISABLED': 'danger'
  }
  return types[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    'ACTIVE': '活跃',
    'INACTIVE': '非活跃',
    'DISABLED': '禁用'
  }
  return labels[status] || status
}

const getRetentionColor = (rate) => {
  if (rate >= 80) return '#67c23a'
  if (rate >= 60) return '#e6a23c'
  if (rate >= 40) return '#f56c6c'
  return '#909399'
}

const formatNumber = (num) => {
  return num.toLocaleString()
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

const exportData = () => {
  ElMessage.success('数据导出功能开发中...')
}

const viewAllUsers = () => {
  ElMessage('查看全部用户功能开发中...')
}

const viewNewUsers = () => {
  ElMessage('查看新增用户功能开发中...')
}

const viewAllUserList = () => {
  ElMessage('查看全部用户列表功能开发中...')
}

const viewUser = (user) => {
  ElMessage(`查看用户 ${user.username} 功能开发中...`)
}

const editUser = (user) => {
  ElMessage(`编辑用户 ${user.username} 功能开发中...`)
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped lang="scss">
.users-statistics-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .page-description {
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .stats-row {
    margin-bottom: 20px;
  }
  
  .filter-card {
    margin-bottom: 20px;
    
    .filter-actions {
      text-align: right;
    }
  }
  
  .charts-row {
    margin-bottom: 20px;
  }
  
  .ranking-row {
    margin-bottom: 20px;
  }
  
  .behavior-row {
    margin-bottom: 20px;
  }
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      
      &.total {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.active {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.new {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.retention {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
    
    .stat-info {
      flex: 1;
      
      .stat-number {
        font-size: 28px;
        font-weight: 700;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.chart-card {
  .chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .chart-placeholder {
      text-align: center;
      color: #909399;
      
      .chart-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      p {
        margin-bottom: 8px;
        font-size: 16px;
      }
      
      small {
        font-size: 12px;
      }
    }
  }
}

.ranking-card {
  .ranking-list {
    .ranking-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f0f2f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .ranking-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
        color: white;
        
        &.gold {
          background: #f7ba2a;
        }
        
        &.silver {
          background: #c0c4cc;
        }
        
        &.bronze {
          background: #b87333;
        }
        
        &.normal {
          background: #909399;
        }
      }
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
        
        .user-details {
          .user-name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .user-email {
            font-size: 12px;
            color: #909399;
          }
        }
      }
      
      .user-stats {
        text-align: right;
        
        .stat-value {
          font-weight: 600;
          color: #409eff;
          margin-bottom: 2px;
        }
        
        .stat-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

.behavior-card {
  .behavior-list {
    .behavior-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid #f0f2f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .behavior-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
      }
      
      .behavior-content {
        flex: 1;
        
        .behavior-title {
          font-size: 14px;
          color: #606266;
          margin-bottom: 4px;
        }
        
        .behavior-value {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }
      }
    }
  }
}

.retention-card {
  .retention-list {
    .retention-item {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .retention-period {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }
      
      .retention-rate {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
    }
  }
}

.table-card {
  .user-info-cell {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .user-details {
      .user-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 2px;
      }
      
      .user-email {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-card {
    .el-row {
      .el-col {
        margin-bottom: 12px;
      }
    }
    
    .filter-actions {
      text-align: center;
    }
  }
  
  .stat-card {
    .stat-content {
      .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
      }
      
      .stat-info {
        .stat-number {
          font-size: 24px;
        }
      }
    }
  }
  
  .ranking-card {
    .ranking-list {
      .ranking-item {
        .user-info {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }
  }
}
</style> 