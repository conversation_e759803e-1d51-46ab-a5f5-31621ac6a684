<template>
  <div class="system-logs-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">系统日志</h1>
        <p class="page-description">查看系统操作日志和错误日志</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="Download" @click="exportLogs">
          导出日志
        </el-button>
        <el-button type="warning" icon="Delete" @click="clearLogs">
          清空日志
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalLogs }}</div>
              <div class="stat-label">总日志数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon error">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.errorLogs }}</div>
              <div class="stat-label">错误日志</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon warning">
              <el-icon><InfoFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.warningLogs }}</div>
              <div class="stat-label">警告日志</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon info">
              <el-icon><Info /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.infoLogs }}</div>
              <div class="stat-label">信息日志</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选器 -->
    <el-card class="filter-card" shadow="never">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="logTypeFilter" placeholder="日志类型" clearable @change="handleFilter">
            <el-option label="全部类型" value="" />
            <el-option label="操作日志" value="OPERATION" />
            <el-option label="错误日志" value="ERROR" />
            <el-option label="系统日志" value="SYSTEM" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="levelFilter" placeholder="日志级别" clearable @change="handleFilter">
            <el-option label="全部级别" value="" />
            <el-option label="ERROR" value="ERROR" />
            <el-option label="WARNING" value="WARNING" />
            <el-option label="INFO" value="INFO" />
            <el-option label="DEBUG" value="DEBUG" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="userFilter"
            placeholder="操作用户"
            clearable
            @input="handleFilter"
          />
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleDateChange"
          />
        </el-col>
        <el-col :span="2">
          <div class="filter-actions">
            <el-button @click="resetFilters">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 日志列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="filteredLogs"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="日志信息" min-width="300">
          <template #default="{ row }">
            <div class="log-info">
              <div class="log-level" :class="getLevelClass(row.level)">
                <el-icon>
                  <component :is="getLevelIcon(row.level)" />
                </el-icon>
                <span>{{ row.level }}</span>
              </div>
              <div class="log-content">
                <div class="log-message">{{ row.message }}</div>
                <div class="log-details">
                  <span class="log-type">{{ getLogTypeLabel(row.logType) }}</span>
                  <span class="log-module">{{ row.module }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="username" label="操作用户" width="120" />
        
        <el-table-column prop="ipAddress" label="IP地址" width="120" />
        
        <el-table-column prop="createTime" label="时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewLogDetail(row)">详情</el-button>
            <el-button size="small" type="danger" @click="deleteLog(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="totalLogs"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量操作 -->
    <div v-if="selectedLogs.length > 0" class="batch-actions">
      <el-card shadow="never">
        <div class="batch-content">
          <span>已选择 {{ selectedLogs.length }} 条日志</span>
          <div class="batch-buttons">
            <el-button size="small" @click="batchExport">批量导出</el-button>
            <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="日志详情"
      width="800px"
    >
      <div v-if="selectedLogDetail" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日志级别">
            <el-tag :type="getLevelType(selectedLogDetail.level)">
              {{ selectedLogDetail.level }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="日志类型">
            {{ getLogTypeLabel(selectedLogDetail.logType) }}
          </el-descriptions-item>
          <el-descriptions-item label="操作用户">
            {{ selectedLogDetail.username }}
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ selectedLogDetail.ipAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="模块">
            {{ selectedLogDetail.module }}
          </el-descriptions-item>
          <el-descriptions-item label="操作时间">
            {{ formatDate(selectedLogDetail.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="用户代理" :span="2">
            <el-tooltip :content="selectedLogDetail.userAgent" placement="top">
              <span class="user-agent">{{ truncateText(selectedLogDetail.userAgent, 100) }}</span>
            </el-tooltip>
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider />
        
        <div class="log-message-section">
          <h3>日志消息</h3>
          <div class="log-message-content">
            {{ selectedLogDetail.message }}
          </div>
        </div>
        
        <el-divider v-if="selectedLogDetail.stackTrace" />
        
        <div v-if="selectedLogDetail.stackTrace" class="stack-trace-section">
          <h3>堆栈信息</h3>
          <pre class="stack-trace">{{ selectedLogDetail.stackTrace }}</pre>
        </div>
        
        <el-divider v-if="selectedLogDetail.requestData" />
        
        <div v-if="selectedLogDetail.requestData" class="request-data-section">
          <h3>请求数据</h3>
          <pre class="request-data">{{ selectedLogDetail.requestData }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Document, 
  Warning, 
  InfoFilled, 
  Info,
  Download,
  Delete
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const logTypeFilter = ref('')
const levelFilter = ref('')
const userFilter = ref('')
const dateRange = ref([])
const currentPage = ref(1)
const pageSize = ref(50)
const totalLogs = ref(0)
const selectedLogs = ref([])
const detailDialogVisible = ref(false)
const selectedLogDetail = ref(null)

// 统计数据
const stats = ref({
  totalLogs: 1234,
  errorLogs: 45,
  warningLogs: 123,
  infoLogs: 1066
})

// 模拟日志数据
const logs = ref([
  {
    id: 1,
    level: 'INFO',
    logType: 'OPERATION',
    message: '用户 admin 登录系统',
    module: '用户认证',
    username: 'admin',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    createTime: '2024-01-27 15:30:00',
    stackTrace: null,
    requestData: null
  },
  {
    id: 2,
    level: 'ERROR',
    logType: 'ERROR',
    message: '数据库连接失败',
    module: '数据库',
    username: 'system',
    ipAddress: '127.0.0.1',
    userAgent: 'System',
    createTime: '2024-01-27 15:25:00',
    stackTrace: 'java.sql.SQLException: Connection refused\n    at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)\n    at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)',
    requestData: '{"url": "/api/apps", "method": "GET", "params": {}}'
  },
  {
    id: 3,
    level: 'WARNING',
    logType: 'SYSTEM',
    message: '磁盘空间不足',
    module: '系统监控',
    username: 'system',
    ipAddress: '127.0.0.1',
    userAgent: 'System',
    createTime: '2024-01-27 15:20:00',
    stackTrace: null,
    requestData: '{"diskUsage": "95%", "availableSpace": "5GB"}'
  }
])

// 计算属性
const filteredLogs = computed(() => {
  let result = logs.value

  // 日志类型过滤
  if (logTypeFilter.value) {
    result = result.filter(log => log.logType === logTypeFilter.value)
  }

  // 级别过滤
  if (levelFilter.value) {
    result = result.filter(log => log.level === levelFilter.value)
  }

  // 用户过滤
  if (userFilter.value) {
    result = result.filter(log => 
      log.username.toLowerCase().includes(userFilter.value.toLowerCase())
    )
  }

  // 时间范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const startTime = new Date(dateRange.value[0])
    const endTime = new Date(dateRange.value[1])
    result = result.filter(log => {
      const logTime = new Date(log.createTime)
      return logTime >= startTime && logTime <= endTime
    })
  }

  return result
})

// 方法
const handleFilter = () => {
  currentPage.value = 1
}

const handleDateChange = () => {
  handleFilter()
}

const resetFilters = () => {
  logTypeFilter.value = ''
  levelFilter.value = ''
  userFilter.value = ''
  dateRange.value = []
  handleFilter()
}

const handleSelectionChange = (selection) => {
  selectedLogs.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const getLevelClass = (level) => {
  const classes = {
    'ERROR': 'error',
    'WARNING': 'warning',
    'INFO': 'info',
    'DEBUG': 'debug'
  }
  return classes[level] || 'info'
}

const getLevelIcon = (level) => {
  const icons = {
    'ERROR': 'Warning',
    'WARNING': 'InfoFilled',
    'INFO': 'Info',
    'DEBUG': 'Document'
  }
  return icons[level] || 'Info'
}

const getLevelType = (level) => {
  const types = {
    'ERROR': 'danger',
    'WARNING': 'warning',
    'INFO': 'info',
    'DEBUG': 'info'
  }
  return types[level] || 'info'
}

const getLogTypeLabel = (logType) => {
  const labels = {
    'OPERATION': '操作日志',
    'ERROR': '错误日志',
    'SYSTEM': '系统日志'
  }
  return labels[logType] || logType
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

const truncateText = (text, maxLength) => {
  if (!text || text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const viewLogDetail = (log) => {
  selectedLogDetail.value = log
  detailDialogVisible.value = true
}

const deleteLog = async (log) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条日志吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = logs.value.findIndex(item => item.id === log.id)
    if (index > -1) {
      logs.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消
  }
}

const exportLogs = () => {
  ElMessage.success('日志导出功能开发中...')
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可恢复！',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    logs.value = []
    ElMessage.success('日志清空成功')
  } catch {
    // 用户取消
  }
}

const batchExport = () => {
  ElMessage.success('批量导出功能开发中...')
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedLogs.value.length} 条日志吗？`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedLogs.value.map(log => log.id)
    logs.value = logs.value.filter(log => !ids.includes(log.id))
    
    ElMessage.success('批量删除成功')
  } catch {
    // 用户取消
  }
}

// 生命周期
onMounted(() => {
  totalLogs.value = logs.value.length
})
</script>

<style scoped lang="scss">
.system-logs-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .page-description {
        color: #909399;
        font-size: 14px;
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
    }
  }
  
  .stats-row {
    margin-bottom: 20px;
  }
  
  .filter-card {
    margin-bottom: 20px;
    
    .filter-actions {
      text-align: center;
    }
  }
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      
      &.total {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.error {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.warning {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.info {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
    
    .stat-info {
      flex: 1;
      
      .stat-number {
        font-size: 28px;
        font-weight: 700;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.table-card {
  .log-info {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    
    .log-level {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
      min-width: 60px;
      justify-content: center;
      
      &.error {
        background: #fef0f0;
        color: #f56c6c;
      }
      
      &.warning {
        background: #fdf6ec;
        color: #e6a23c;
      }
      
      &.info {
        background: #f0f9ff;
        color: #409eff;
      }
      
      &.debug {
        background: #f4f4f5;
        color: #909399;
      }
    }
    
    .log-content {
      flex: 1;
      
      .log-message {
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .log-details {
        display: flex;
        gap: 12px;
        font-size: 12px;
        color: #909399;
        
        .log-type {
          background: #f0f2f5;
          padding: 2px 6px;
          border-radius: 3px;
        }
        
        .log-module {
          background: #f0f2f5;
          padding: 2px 6px;
          border-radius: 3px;
        }
      }
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  
  .batch-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    
    .batch-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

.log-detail {
  .log-message-section,
  .stack-trace-section,
  .request-data-section {
    h3 {
      margin-bottom: 12px;
      color: #303133;
    }
  }
  
  .log-message-content {
    background: #f5f7fa;
    padding: 16px;
    border-radius: 6px;
    font-family: monospace;
    white-space: pre-wrap;
    word-break: break-all;
  }
  
  .stack-trace,
  .request-data {
    background: #f5f7fa;
    padding: 16px;
    border-radius: 6px;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 300px;
    overflow-y: auto;
  }
  
  .user-agent {
    font-family: monospace;
    font-size: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    .header-right {
      width: 100%;
      justify-content: flex-end;
    }
  }
  
  .filter-card {
    .el-row {
      .el-col {
        margin-bottom: 12px;
      }
    }
    
    .filter-actions {
      text-align: center;
    }
  }
  
  .stat-card {
    .stat-content {
      .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
      }
      
      .stat-info {
        .stat-number {
          font-size: 24px;
        }
      }
    }
  }
  
  .batch-actions {
    position: static;
    transform: none;
    margin-top: 20px;
    
    .batch-content {
      flex-direction: column;
      gap: 12px;
    }
  }
}
</style> 