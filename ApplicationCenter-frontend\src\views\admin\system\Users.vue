<template>
  <div class="user-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">用户管理</h1>
        <p class="page-description">管理系统管理员账户</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="Plus" @click="showAddDialog">
          添加用户
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card" shadow="never">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索用户名或邮箱"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="roleFilter" placeholder="角色" clearable @change="handleFilter">
            <el-option label="全部角色" value="" />
            <el-option label="超级管理员" value="SUPER_ADMIN" />
            <el-option label="管理员" value="ADMIN" />
            <el-option label="操作员" value="OPERATOR" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态" clearable @change="handleFilter">
            <el-option label="全部状态" value="" />
            <el-option label="启用" value="ENABLED" />
            <el-option label="禁用" value="DISABLED" />
          </el-select>
        </el-col>
        <el-col :span="10">
          <div class="filter-actions">
            <el-button @click="resetFilters">重置</el-button>
            <el-button type="primary" @click="refreshData">刷新</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="filteredUsers"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="用户信息" min-width="250">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="48" :src="row.avatar" />
              <div class="user-details">
                <div class="user-name">{{ row.username }}</div>
                <div class="user-email">{{ row.email }}</div>
                <div class="user-role">
                  <el-tag size="small" :type="getRoleType(row.role)">
                    {{ getRoleLabel(row.role) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="realName" label="真实姓名" width="120" />
        
        <el-table-column prop="phone" label="手机号" width="130" />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'ENABLED' ? 'success' : 'danger'">
              {{ row.status === 'ENABLED' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastLoginTime" label="最后登录" width="180" sortable>
          <template #default="{ row }">
            {{ formatDate(row.lastLoginTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewUser(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editUser(row)">编辑</el-button>
            <el-button 
              size="small" 
              :type="row.status === 'ENABLED' ? 'warning' : 'success'"
              @click="toggleUserStatus(row)"
            >
              {{ row.status === 'ENABLED' ? '禁用' : '启用' }}
            </el-button>
            <el-button size="small" type="danger" @click="deleteUser(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalUsers"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量操作 -->
    <div v-if="selectedUsers.length > 0" class="batch-actions">
      <el-card shadow="never">
        <div class="batch-content">
          <span>已选择 {{ selectedUsers.length }} 个用户</span>
          <div class="batch-buttons">
            <el-button size="small" @click="batchEnable">批量启用</el-button>
            <el-button size="small" type="warning" @click="batchDisable">批量禁用</el-button>
            <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
      width="600px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="超级管理员" value="SUPER_ADMIN" />
            <el-option label="管理员" value="ADMIN" />
            <el-option label="操作员" value="OPERATOR" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="dialogType === 'add'" label="密码" prop="password">
          <el-input 
            v-model="userForm.password" 
            type="password" 
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item v-if="dialogType === 'add'" label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="userForm.confirmPassword" 
            type="password" 
            placeholder="请确认密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio label="ENABLED">启用</el-radio>
            <el-radio label="DISABLED">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="userForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUser" :loading="submitting">
            {{ dialogType === 'add' ? '添加' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="用户详情"
      width="700px"
    >
      <div v-if="selectedUserDetail" class="user-detail">
        <div class="detail-header">
          <el-avatar :size="80" :src="selectedUserDetail.avatar" />
          <div class="detail-info">
            <h2>{{ selectedUserDetail.username }}</h2>
            <p>{{ selectedUserDetail.email }}</p>
            <div class="detail-meta">
              <el-tag :type="getRoleType(selectedUserDetail.role)">
                {{ getRoleLabel(selectedUserDetail.role) }}
              </el-tag>
              <el-tag :type="selectedUserDetail.status === 'ENABLED' ? 'success' : 'danger'">
                {{ selectedUserDetail.status === 'ENABLED' ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <el-divider />
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="真实姓名">
            {{ selectedUserDetail.realName }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ selectedUserDetail.phone }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录时间">
            {{ formatDate(selectedUserDetail.lastLoginTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(selectedUserDetail.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ selectedUserDetail.remark || '无' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider />
        
        <div class="user-permissions">
          <h3>权限信息</h3>
          <el-tag
            v-for="permission in getUserPermissions(selectedUserDetail.role)"
            :key="permission"
            class="permission-tag"
          >
            {{ permission }}
          </el-tag>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const roleFilter = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalUsers = ref(0)
const selectedUsers = ref([])
const dialogVisible = ref(false)
const dialogType = ref('add')
const detailDialogVisible = ref(false)
const selectedUserDetail = ref(null)
const submitting = ref(false)

const userFormRef = ref()

// 表单数据
const userForm = reactive({
  username: '',
  email: '',
  realName: '',
  phone: '',
  role: '',
  password: '',
  confirmPassword: '',
  status: 'ENABLED',
  remark: ''
})

// 表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== userForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 模拟用户数据
const users = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    realName: '系统管理员',
    phone: '13800138000',
    role: 'SUPER_ADMIN',
    status: 'ENABLED',
    avatar: '',
    lastLoginTime: '2024-01-27 15:30:00',
    createTime: '2024-01-01 10:00:00',
    remark: '系统超级管理员'
  },
  {
    id: 2,
    username: 'operator1',
    email: '<EMAIL>',
    realName: '操作员1',
    phone: '13800138001',
    role: 'OPERATOR',
    status: 'ENABLED',
    avatar: '',
    lastLoginTime: '2024-01-27 14:20:00',
    createTime: '2024-01-15 09:30:00',
    remark: '应用管理操作员'
  }
])

// 计算属性
const filteredUsers = computed(() => {
  let result = users.value

  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(user => 
      user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      user.realName.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // 角色过滤
  if (roleFilter.value) {
    result = result.filter(user => user.role === roleFilter.value)
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(user => user.status === statusFilter.value)
  }

  return result
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchQuery.value = ''
  roleFilter.value = ''
  statusFilter.value = ''
  currentPage.value = 1
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const getRoleType = (role) => {
  const types = {
    'SUPER_ADMIN': 'danger',
    'ADMIN': 'warning',
    'OPERATOR': 'info'
  }
  return types[role] || 'info'
}

const getRoleLabel = (role) => {
  const labels = {
    'SUPER_ADMIN': '超级管理员',
    'ADMIN': '管理员',
    'OPERATOR': '操作员'
  }
  return labels[role] || role
}

const getUserPermissions = (role) => {
  const permissions = {
    'SUPER_ADMIN': ['系统管理', '用户管理', '应用管理', '版本管理', '数据统计', '系统设置'],
    'ADMIN': ['应用管理', '版本管理', '数据统计'],
    'OPERATOR': ['应用管理', '版本管理']
  }
  return permissions[role] || []
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

const showAddDialog = () => {
  dialogType.value = 'add'
  resetUserForm()
  dialogVisible.value = true
}

const editUser = (user) => {
  dialogType.value = 'edit'
  Object.assign(userForm, user)
  dialogVisible.value = true
}

const viewUser = (user) => {
  selectedUserDetail.value = user
  detailDialogVisible.value = true
}

const toggleUserStatus = async (user) => {
  const action = user.status === 'ENABLED' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.username}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = users.value.findIndex(item => item.id === user.id)
    if (index > -1) {
      users.value[index].status = user.status === 'ENABLED' ? 'DISABLED' : 'ENABLED'
      ElMessage.success(`${action}成功`)
    }
  } catch {
    // 用户取消
  }
}

const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = users.value.findIndex(item => item.id === user.id)
    if (index > -1) {
      users.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消
  }
}

const resetUserForm = () => {
  Object.assign(userForm, {
    username: '',
    email: '',
    realName: '',
    phone: '',
    role: '',
    password: '',
    confirmPassword: '',
    status: 'ENABLED',
    remark: ''
  })
}

const submitUser = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    submitting.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (dialogType.value === 'add') {
      const newUser = {
        id: Date.now(),
        ...userForm,
        avatar: '',
        lastLoginTime: '',
        createTime: new Date().toLocaleString()
      }
      delete newUser.password
      delete newUser.confirmPassword
      users.value.unshift(newUser)
      ElMessage.success('添加成功')
    } else {
      const index = users.value.findIndex(item => item.id === userForm.id)
      if (index > -1) {
        users.value[index] = { ...users.value[index], ...userForm }
        ElMessage.success('更新成功')
      }
    }
    
    dialogVisible.value = false
  } catch (error) {
    console.error('提交错误:', error)
  } finally {
    submitting.value = false
  }
}

const batchEnable = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要启用选中的 ${selectedUsers.value.length} 个用户吗？`,
      '批量操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    selectedUsers.value.forEach(user => {
      const index = users.value.findIndex(item => item.id === user.id)
      if (index > -1) {
        users.value[index].status = 'ENABLED'
      }
    })
    
    ElMessage.success('批量启用成功')
  } catch {
    // 用户取消
  }
}

const batchDisable = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用选中的 ${selectedUsers.value.length} 个用户吗？`,
      '批量操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    selectedUsers.value.forEach(user => {
      const index = users.value.findIndex(item => item.id === user.id)
      if (index > -1) {
        users.value[index].status = 'DISABLED'
      }
    })
    
    ElMessage.success('批量禁用成功')
  } catch {
    // 用户取消
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复！`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedUsers.value.map(user => user.id)
    users.value = users.value.filter(user => !ids.includes(user.id))
    
    ElMessage.success('批量删除成功')
  } catch {
    // 用户取消
  }
}

// 生命周期
onMounted(() => {
  totalUsers.value = users.value.length
})
</script>

<style scoped lang="scss">
.user-management-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .page-description {
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

.filter-card {
  margin-bottom: 20px;
  
  .filter-actions {
    text-align: right;
  }
}

.table-card {
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .user-details {
      flex: 1;
      
      .user-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .user-email {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }
      
      .user-role {
        display: flex;
        gap: 4px;
      }
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  
  .batch-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    
    .batch-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

.user-detail {
  .detail-header {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    
    .detail-info {
      flex: 1;
      
      h2 {
        margin-bottom: 8px;
        color: #303133;
      }
      
      p {
        color: #606266;
        margin-bottom: 12px;
      }
      
      .detail-meta {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }
  }
  
  .user-permissions {
    h3 {
      margin-bottom: 16px;
      color: #303133;
    }
    
    .permission-tag {
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-card {
    .el-row {
      .el-col {
        margin-bottom: 12px;
      }
    }
    
    .filter-actions {
      text-align: center;
    }
  }
  
  .batch-actions {
    position: static;
    transform: none;
    margin-top: 20px;
    
    .batch-content {
      flex-direction: column;
      gap: 12px;
    }
  }
}
</style> 