<template>
  <div class="api-docs-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">API文档</h1>
        <p class="page-description">APP版本管理系统接口文档</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="Download" @click="downloadDocs">
          下载文档
        </el-button>
        <el-button type="success" icon="Link" @click="copyBaseUrl">
          复制Base URL
        </el-button>
      </div>
    </div>

    <!-- API概览 -->
    <el-card class="overview-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>API概览</span>
        </div>
      </template>
      
      <div class="overview-content">
        <div class="overview-item">
          <div class="overview-icon">
            <el-icon><Link /></el-icon>
          </div>
          <div class="overview-info">
            <div class="overview-title">Base URL</div>
            <div class="overview-value">{{ baseUrl }}</div>
          </div>
        </div>
        
        <div class="overview-item">
          <div class="overview-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="overview-info">
            <div class="overview-title">接口总数</div>
            <div class="overview-value">{{ totalApis }}</div>
          </div>
        </div>
        
        <div class="overview-item">
          <div class="overview-icon">
            <el-icon><Lock /></el-icon>
          </div>
          <div class="overview-info">
            <div class="overview-title">认证方式</div>
            <div class="overview-value">JWT Token</div>
          </div>
        </div>
        
        <div class="overview-item">
          <div class="overview-icon">
            <el-icon><DataFormat /></el-icon>
          </div>
          <div class="overview-info">
            <div class="overview-title">数据格式</div>
            <div class="overview-value">JSON</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 接口分类 -->
    <el-card class="categories-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>接口分类</span>
        </div>
      </template>
      
      <el-tabs v-model="activeCategory" @tab-click="handleCategoryChange">
        <el-tab-pane 
          v-for="category in apiCategories" 
          :key="category.key"
          :label="category.name" 
          :name="category.key"
        >
          <div class="api-list">
            <div
              v-for="api in category.apis"
              :key="api.id"
              class="api-item"
              :class="{ expanded: expandedApis.includes(api.id) }"
            >
              <div class="api-header" @click="toggleApi(api.id)">
                <div class="api-method" :class="getMethodClass(api.method)">
                  {{ api.method }}
                </div>
                <div class="api-path">{{ api.path }}</div>
                <div class="api-name">{{ api.name }}</div>
                <div class="api-actions">
                  <el-button size="small" @click.stop="testApi(api)">测试</el-button>
                  <el-icon class="expand-icon">
                    <component :is="expandedApis.includes(api.id) ? 'ArrowUp' : 'ArrowDown'" />
                  </el-icon>
                </div>
              </div>
              
              <div v-show="expandedApis.includes(api.id)" class="api-details">
                <el-divider />
                
                <div class="api-description">
                  <h4>接口描述</h4>
                  <p>{{ api.description }}</p>
                </div>
                
                <div class="api-params">
                  <h4>请求参数</h4>
                  <el-table :data="api.parameters" border style="width: 100%">
                    <el-table-column prop="name" label="参数名" width="150" />
                    <el-table-column prop="type" label="类型" width="100" />
                    <el-table-column prop="required" label="必填" width="80">
                      <template #default="{ row }">
                        <el-tag :type="row.required ? 'danger' : 'info'" size="small">
                          {{ row.required ? '是' : '否' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="description" label="说明" />
                  </el-table>
                </div>
                
                <div class="api-response">
                  <h4>响应示例</h4>
                  <el-tabs v-model="api.activeTab">
                    <el-tab-pane label="成功响应" name="success">
                      <pre class="response-example">{{ api.successResponse }}</pre>
                    </el-tab-pane>
                    <el-tab-pane label="错误响应" name="error">
                      <pre class="response-example">{{ api.errorResponse }}</pre>
                    </el-tab-pane>
                  </el-tabs>
                </div>
                
                <div class="api-headers">
                  <h4>请求头</h4>
                  <el-table :data="api.headers" border style="width: 100%">
                    <el-table-column prop="name" label="Header" width="200" />
                    <el-table-column prop="value" label="值" />
                    <el-table-column prop="description" label="说明" width="200" />
                  </el-table>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 认证说明 -->
    <el-card class="auth-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>认证说明</span>
        </div>
      </template>
      
      <div class="auth-content">
        <h3>JWT Token认证</h3>
        <p>系统使用JWT Token进行身份认证，需要在请求头中携带Token。</p>
        
        <div class="auth-steps">
          <div class="auth-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4>获取Token</h4>
              <p>通过登录接口获取JWT Token</p>
              <pre class="code-example">POST /api/auth/login
{
  "username": "admin",
  "password": "admin123"
}</pre>
            </div>
          </div>
          
          <div class="auth-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4>使用Token</h4>
              <p>在请求头中添加Authorization字段</p>
              <pre class="code-example">Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...</pre>
            </div>
          </div>
          
          <div class="auth-step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4>Token刷新</h4>
              <p>Token过期时使用刷新接口获取新Token</p>
              <pre class="code-example">POST /api/auth/refresh
Authorization: Bearer [refresh_token]</pre>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 错误码说明 -->
    <el-card class="error-codes-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>错误码说明</span>
        </div>
      </template>
      
      <el-table :data="errorCodes" border style="width: 100%">
        <el-table-column prop="code" label="错误码" width="100" />
        <el-table-column prop="message" label="错误信息" />
        <el-table-column prop="description" label="说明" />
        <el-table-column prop="solution" label="解决方案" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Download, 
  Link, 
  Document, 
  Lock, 
  DataFormat,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'

// 响应式数据
const activeCategory = ref('auth')
const expandedApis = ref([])
const baseUrl = ref('http://localhost:8080/api')

// 计算属性
const totalApis = computed(() => {
  return apiCategories.value.reduce((total, category) => total + category.apis.length, 0)
})

// API分类数据
const apiCategories = ref([
  {
    key: 'auth',
    name: '认证接口',
    apis: [
      {
        id: 'auth-login',
        method: 'POST',
        path: '/auth/login',
        name: '用户登录',
        description: '管理员用户登录接口，返回JWT Token',
        parameters: [
          { name: 'username', type: 'string', required: true, description: '用户名' },
          { name: 'password', type: 'string', required: true, description: '密码' }
        ],
        headers: [
          { name: 'Content-Type', value: 'application/json', description: '请求内容类型' }
        ],
        successResponse: `{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "username": "admin",
    "expiresIn": 3600
  }
}`,
        errorResponse: `{
  "code": 401,
  "message": "用户名或密码错误",
  "data": null
}`,
        activeTab: 'success'
      },
      {
        id: 'auth-refresh',
        method: 'POST',
        path: '/auth/refresh',
        name: '刷新Token',
        description: '使用刷新Token获取新的访问Token',
        parameters: [],
        headers: [
          { name: 'Authorization', value: 'Bearer [refresh_token]', description: '刷新Token' }
        ],
        successResponse: `{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600
  }
}`,
        errorResponse: `{
  "code": 401,
  "message": "刷新Token无效",
  "data": null
}`,
        activeTab: 'success'
      }
    ]
  },
  {
    key: 'apps',
    name: '应用管理',
    apis: [
      {
        id: 'apps-list',
        method: 'GET',
        path: '/apps',
        name: '获取应用列表',
        description: '分页获取应用列表，支持搜索和筛选',
        parameters: [
          { name: 'page', type: 'integer', required: false, description: '页码，默认1' },
          { name: 'size', type: 'integer', required: false, description: '每页数量，默认20' },
          { name: 'search', type: 'string', required: false, description: '搜索关键词' },
          { name: 'platform', type: 'string', required: false, description: '平台筛选' }
        ],
        headers: [
          { name: 'Authorization', value: 'Bearer [token]', description: '访问Token' }
        ],
        successResponse: `{
  "code": 200,
  "message": "获取成功",
  "data": {
    "content": [
      {
        "id": 1,
        "name": "示例应用",
        "description": "应用描述",
        "platform": "ANDROID",
        "latestVersion": "1.2.3",
        "downloadCount": 1234
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "currentPage": 1
  }
}`,
        errorResponse: `{
  "code": 401,
  "message": "未授权访问",
  "data": null
}`,
        activeTab: 'success'
      },
      {
        id: 'apps-create',
        method: 'POST',
        path: '/apps',
        name: '创建应用',
        description: '创建新的应用',
        parameters: [
          { name: 'name', type: 'string', required: true, description: '应用名称' },
          { name: 'description', type: 'string', required: true, description: '应用描述' },
          { name: 'platform', type: 'string', required: true, description: '平台类型' },
          { name: 'category', type: 'string', required: false, description: '应用分类' }
        ],
        headers: [
          { name: 'Authorization', value: 'Bearer [token]', description: '访问Token' },
          { name: 'Content-Type', value: 'application/json', description: '请求内容类型' }
        ],
        successResponse: `{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "name": "示例应用",
    "description": "应用描述",
    "platform": "ANDROID",
    "createTime": "2024-01-27T15:30:00"
  }
}`,
        errorResponse: `{
  "code": 400,
  "message": "参数错误",
  "data": null
}`,
        activeTab: 'success'
      }
    ]
  },
  {
    key: 'versions',
    name: '版本管理',
    apis: [
      {
        id: 'versions-list',
        method: 'GET',
        path: '/versions',
        name: '获取版本列表',
        description: '分页获取版本列表，支持应用筛选',
        parameters: [
          { name: 'appId', type: 'integer', required: false, description: '应用ID' },
          { name: 'page', type: 'integer', required: false, description: '页码' },
          { name: 'size', type: 'integer', required: false, description: '每页数量' }
        ],
        headers: [
          { name: 'Authorization', value: 'Bearer [token]', description: '访问Token' }
        ],
        successResponse: `{
  "code": 200,
  "message": "获取成功",
  "data": {
    "content": [
      {
        "id": 1,
        "appId": 1,
        "versionNumber": "1.2.3",
        "buildNumber": "123",
        "description": "版本描述",
        "fileSize": 25600000,
        "downloadCount": 1234
      }
    ],
    "totalElements": 1,
    "totalPages": 1
  }
}`,
        errorResponse: `{
  "code": 404,
  "message": "应用不存在",
  "data": null
}`,
        activeTab: 'success'
      }
    ]
  },
  {
    key: 'statistics',
    name: '统计分析',
    apis: [
      {
        id: 'stats-overview',
        method: 'GET',
        path: '/statistics/overview',
        name: '统计概览',
        description: '获取系统统计概览数据',
        parameters: [],
        headers: [
          { name: 'Authorization', value: 'Bearer [token]', description: '访问Token' }
        ],
        successResponse: `{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalApps": 25,
    "totalVersions": 156,
    "totalDownloads": 12345,
    "totalUsers": 3
  }
}`,
        errorResponse: `{
  "code": 403,
  "message": "权限不足",
  "data": null
}`,
        activeTab: 'success'
      }
    ]
  }
])

// 错误码数据
const errorCodes = ref([
  { code: 200, message: '成功', description: '请求成功', solution: '-' },
  { code: 400, message: '请求错误', description: '请求参数错误', solution: '检查请求参数格式' },
  { code: 401, message: '未授权', description: '用户未登录或Token无效', solution: '重新登录获取Token' },
  { code: 403, message: '禁止访问', description: '用户权限不足', solution: '联系管理员分配权限' },
  { code: 404, message: '资源不存在', description: '请求的资源不存在', solution: '检查资源ID是否正确' },
  { code: 500, message: '服务器错误', description: '服务器内部错误', solution: '联系技术支持' }
])

// 方法
const handleCategoryChange = (tab) => {
  // 切换分类时的处理
}

const toggleApi = (apiId) => {
  const index = expandedApis.value.indexOf(apiId)
  if (index > -1) {
    expandedApis.value.splice(index, 1)
  } else {
    expandedApis.value.push(apiId)
  }
}

const getMethodClass = (method) => {
  const classes = {
    'GET': 'get',
    'POST': 'post',
    'PUT': 'put',
    'DELETE': 'delete',
    'PATCH': 'patch'
  }
  return classes[method] || 'get'
}

const testApi = (api) => {
  ElMessage.success(`测试接口: ${api.name}`)
}

const downloadDocs = () => {
  ElMessage.success('文档下载功能开发中...')
}

const copyBaseUrl = () => {
  navigator.clipboard.writeText(baseUrl.value).then(() => {
    ElMessage.success('Base URL已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped lang="scss">
.api-docs-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .page-description {
        color: #909399;
        font-size: 14px;
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
    }
  }
}

.overview-card {
  margin-bottom: 20px;
  
  .overview-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    
    .overview-item {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .overview-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
      }
      
      .overview-info {
        flex: 1;
        
        .overview-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 4px;
        }
        
        .overview-value {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }
    }
  }
}

.categories-card {
  margin-bottom: 20px;
  
  .api-list {
    .api-item {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 12px;
      overflow: hidden;
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }
      
      &.expanded {
        border-color: #409eff;
      }
      
      .api-header {
        display: flex;
        align-items: center;
        padding: 16px;
        cursor: pointer;
        background: #fafafa;
        
        .api-method {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 600;
          color: white;
          min-width: 60px;
          text-align: center;
          
          &.get {
            background: #67c23a;
          }
          
          &.post {
            background: #409eff;
          }
          
          &.put {
            background: #e6a23c;
          }
          
          &.delete {
            background: #f56c6c;
          }
          
          &.patch {
            background: #909399;
          }
        }
        
        .api-path {
          flex: 1;
          margin-left: 16px;
          font-family: monospace;
          font-size: 14px;
          color: #606266;
        }
        
        .api-name {
          margin-left: 16px;
          font-weight: 600;
          color: #303133;
        }
        
        .api-actions {
          margin-left: 16px;
          display: flex;
          align-items: center;
          gap: 8px;
          
          .expand-icon {
            font-size: 16px;
            color: #909399;
            transition: transform 0.3s;
          }
        }
      }
      
      .api-details {
        padding: 20px;
        
        h4 {
          margin-bottom: 12px;
          color: #303133;
          font-size: 16px;
        }
        
        .api-description {
          margin-bottom: 24px;
          
          p {
            color: #606266;
            line-height: 1.6;
          }
        }
        
        .api-params,
        .api-response,
        .api-headers {
          margin-bottom: 24px;
        }
        
        .response-example {
          background: #f5f7fa;
          padding: 16px;
          border-radius: 6px;
          font-family: monospace;
          font-size: 12px;
          white-space: pre-wrap;
          word-break: break-all;
          max-height: 300px;
          overflow-y: auto;
        }
      }
    }
  }
}

.auth-card {
  margin-bottom: 20px;
  
  .auth-content {
    h3 {
      margin-bottom: 16px;
      color: #303133;
    }
    
    p {
      color: #606266;
      margin-bottom: 24px;
      line-height: 1.6;
    }
    
    .auth-steps {
      .auth-step {
        display: flex;
        gap: 16px;
        margin-bottom: 24px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .step-number {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #409eff;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          flex-shrink: 0;
        }
        
        .step-content {
          flex: 1;
          
          h4 {
            margin-bottom: 8px;
            color: #303133;
          }
          
          p {
            margin-bottom: 12px;
            color: #606266;
          }
          
          .code-example {
            background: #f5f7fa;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
          }
        }
      }
    }
  }
}

.error-codes-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    .header-right {
      width: 100%;
      justify-content: flex-end;
    }
  }
  
  .overview-card {
    .overview-content {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
  
  .categories-card {
    .api-list {
      .api-item {
        .api-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
          
          .api-path {
            margin-left: 0;
          }
          
          .api-name {
            margin-left: 0;
          }
          
          .api-actions {
            margin-left: 0;
            width: 100%;
            justify-content: space-between;
          }
        }
      }
    }
  }
}
</style> 