"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/// <reference types="D:/桌面/临时文件/A源码/APP版本管理系统重构/ApplicationCenter-frontend/node_modules/.vue-global-types/vue_3.5_0.d.ts" />
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
var __VLS_ctx = {};
var __VLS_elements;
var __VLS_components;
var __VLS_directives;
var __VLS_self = (await Promise.resolve().then(function () { return require('vue'); })).defineComponent({
    setup: function () {
        return {};
    },
});
exports.default = (await Promise.resolve().then(function () { return require('vue'); })).defineComponent({
    setup: function () {
    },
});
; /* PartiallyEnd: #4569/main.vue */
