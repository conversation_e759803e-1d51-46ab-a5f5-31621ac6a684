<template>
  <div class="home-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">应用下载中心</h1>
        <p class="page-description">发现和下载最新版本的应用程序</p>
        
        <!-- 搜索框 -->
        <div class="search-container">
          <el-input
            v-model="searchQuery"
            placeholder="搜索应用名称..."
            class="search-input"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 筛选器 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select v-model="platformFilter" placeholder="选择平台" clearable @change="handleFilter">
              <el-option label="全部平台" value="" />
              <el-option label="Android" value="ANDROID" />
              <el-option label="iOS" value="IOS" />
              <el-option label="Windows" value="WINDOWS" />
              <el-option label="macOS" value="MACOS" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="categoryFilter" placeholder="选择分类" clearable @change="handleFilter">
              <el-option label="全部分类" value="" />
              <el-option label="工具" value="工具" />
              <el-option label="娱乐" value="娱乐" />
              <el-option label="教育" value="教育" />
              <el-option label="商务" value="商务" />
            </el-select>
          </el-col>
          <el-col :span="12">
            <div class="filter-actions">
              <el-button @click="resetFilters">重置筛选</el-button>
              <el-button type="primary" @click="goToAdmin">管理员入口</el-button>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 应用列表 -->
      <div class="app-list">
        <el-row :gutter="20">
          <el-col 
            v-for="app in filteredApps" 
            :key="app.id" 
            :xs="24" 
            :sm="12" 
            :md="8" 
            :lg="6"
            class="app-item"
          >
            <el-card class="app-card" shadow="hover">
              <div class="app-icon">
                <el-image 
                  :src="app.iconUrl || '/default-app-icon.png'" 
                  fit="cover"
                  class="icon-image"
                >
                  <template #error>
                    <div class="icon-placeholder">
                      <el-icon><App /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              
              <div class="app-info">
                <h3 class="app-name">{{ app.name }}</h3>
                <p class="app-description">{{ app.description }}</p>
                <div class="app-meta">
                  <el-tag size="small" :type="getPlatformType(app.platform)">
                    {{ getPlatformLabel(app.platform) }}
                  </el-tag>
                  <span class="version">v{{ app.latestVersion }}</span>
                </div>
                
                <div class="app-actions">
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="downloadApp(app)"
                    :loading="app.downloading"
                  >
                    下载
                  </el-button>
                  <el-button 
                    size="small" 
                    @click="viewAppDetail(app)"
                  >
                    详情
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 48, 96]"
          :total="totalApps"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 应用详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="应用详情"
      width="600px"
    >
      <div v-if="selectedApp" class="app-detail">
        <div class="detail-header">
          <el-image 
            :src="selectedApp.iconUrl || '/default-app-icon.png'" 
            fit="cover"
            class="detail-icon"
          />
          <div class="detail-info">
            <h2>{{ selectedApp.name }}</h2>
            <p>{{ selectedApp.description }}</p>
            <div class="detail-meta">
              <el-tag :type="getPlatformType(selectedApp.platform)">
                {{ getPlatformLabel(selectedApp.platform) }}
              </el-tag>
              <span class="version">v{{ selectedApp.latestVersion }}</span>
            </div>
          </div>
        </div>
        
        <div class="version-list">
          <h3>版本历史</h3>
          <el-timeline>
            <el-timeline-item
              v-for="version in selectedApp.versions"
              :key="version.id"
              :timestamp="version.releaseDate"
              placement="top"
            >
              <el-card>
                <h4>v{{ version.versionNumber }}</h4>
                <p>{{ version.description }}</p>
                <el-button size="small" @click="downloadVersion(version)">
                  下载此版本
                </el-button>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, App } from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const platformFilter = ref('')
const categoryFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const totalApps = ref(0)
const detailDialogVisible = ref(false)
const selectedApp = ref(null)

// 模拟应用数据
const apps = ref([
  {
    id: 1,
    name: '示例应用1',
    description: '这是一个示例应用的描述信息',
    platform: 'ANDROID',
    category: '工具',
    latestVersion: '1.2.3',
    iconUrl: '',
    downloading: false,
    versions: [
      { id: 1, versionNumber: '1.2.3', description: '最新版本', releaseDate: '2024-01-15' },
      { id: 2, versionNumber: '1.2.2', description: '修复bug', releaseDate: '2024-01-10' }
    ]
  },
  {
    id: 2,
    name: '示例应用2',
    description: '另一个示例应用的描述',
    platform: 'IOS',
    category: '娱乐',
    latestVersion: '2.1.0',
    iconUrl: '',
    downloading: false,
    versions: [
      { id: 3, versionNumber: '2.1.0', description: '新功能发布', releaseDate: '2024-01-20' }
    ]
  }
])

// 计算属性
const filteredApps = computed(() => {
  let result = apps.value

  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(app => 
      app.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      app.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // 平台过滤
  if (platformFilter.value) {
    result = result.filter(app => app.platform === platformFilter.value)
  }

  // 分类过滤
  if (categoryFilter.value) {
    result = result.filter(app => app.category === categoryFilter.value)
  }

  return result
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchQuery.value = ''
  platformFilter.value = ''
  categoryFilter.value = ''
  currentPage.value = 1
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const getPlatformType = (platform) => {
  const types = {
    'ANDROID': 'success',
    'IOS': 'primary',
    'WINDOWS': 'info',
    'MACOS': 'warning'
  }
  return types[platform] || 'info'
}

const getPlatformLabel = (platform) => {
  const labels = {
    'ANDROID': 'Android',
    'IOS': 'iOS',
    'WINDOWS': 'Windows',
    'MACOS': 'macOS'
  }
  return labels[platform] || platform
}

const downloadApp = async (app) => {
  app.downloading = true
  try {
    // 模拟下载
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success(`开始下载 ${app.name}`)
  } catch (error) {
    ElMessage.error('下载失败')
  } finally {
    app.downloading = false
  }
}

const viewAppDetail = (app) => {
  selectedApp.value = app
  detailDialogVisible.value = true
}

const downloadVersion = (version) => {
  ElMessage.success(`开始下载版本 ${version.versionNumber}`)
}

const goToAdmin = () => {
  router.push('/login')
}

// 生命周期
onMounted(() => {
  totalApps.value = apps.value.length
})
</script>

<style scoped lang="scss">
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.page-header {
  padding: 60px 20px 40px;
  text-align: center;
  color: white;
  
  .header-content {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .page-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  }
  
  .page-description {
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.9;
  }
  
  .search-container {
    max-width: 400px;
    margin: 0 auto;
    
    .search-input {
      :deep(.el-input__wrapper) {
        background: rgba(255,255,255,0.9);
        border-radius: 25px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }
    }
  }
}

.main-content {
  background: #f5f7fa;
  min-height: calc(100vh - 200px);
  padding: 40px 20px;
}

.filter-section {
  max-width: 1200px;
  margin: 0 auto 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .filter-actions {
    text-align: right;
  }
}

.app-list {
  max-width: 1200px;
  margin: 0 auto;
  
  .app-item {
    margin-bottom: 20px;
  }
  
  .app-card {
    height: 100%;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .app-icon {
      text-align: center;
      margin-bottom: 16px;
      
      .icon-image {
        width: 80px;
        height: 80px;
        border-radius: 16px;
      }
      
      .icon-placeholder {
        width: 80px;
        height: 80px;
        background: #f0f2f5;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        color: #909399;
      }
    }
    
    .app-info {
      .app-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #303133;
      }
      
      .app-description {
        font-size: 14px;
        color: #606266;
        margin-bottom: 12px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .app-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        .version {
          font-size: 12px;
          color: #909399;
        }
      }
      
      .app-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}

.pagination-section {
  max-width: 1200px;
  margin: 40px auto 0;
  text-align: center;
}

.app-detail {
  .detail-header {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    
    .detail-icon {
      width: 100px;
      height: 100px;
      border-radius: 20px;
    }
    
    .detail-info {
      flex: 1;
      
      h2 {
        margin-bottom: 8px;
        color: #303133;
      }
      
      p {
        color: #606266;
        margin-bottom: 12px;
      }
      
      .detail-meta {
        display: flex;
        gap: 12px;
        align-items: center;
        
        .version {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }
  
  .version-list {
    h3 {
      margin-bottom: 16px;
      color: #303133;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    padding: 40px 20px 30px;
    
    .page-title {
      font-size: 32px;
    }
    
    .page-description {
      font-size: 16px;
    }
  }
  
  .main-content {
    padding: 20px 10px;
  }
  
  .filter-section {
    .filter-actions {
      text-align: center;
      margin-top: 16px;
    }
  }
}
</style> 