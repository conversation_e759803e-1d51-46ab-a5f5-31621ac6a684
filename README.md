# APP版本管理系统

## 项目简介
APP版本管理系统用于集中管理各平台APP的版本信息，实现应用的上传、发布、下架、下载、统计等功能，支持多平台（Android/iOS/鸿蒙），适用于企业或团队的APP分发与版本管理。

## 主要功能
- 应用信息管理（增删改查）
- 版本管理（上传、发布、下架、历史版本）
- 文件上传与分发（本地存储，支持扩展）
- 管理员后台（权限认证、操作日志）
- 公开API（应用下载、版本信息查询）
- 数据统计与日志审计

## 技术架构
- 后端：Java（Spring Boot）
- 数据库：MySQL
- 文件存储：本地文件系统（可扩展OSS/MinIO）
- 前端：前后端分离，API接口RESTful
- 认证：JWT Token

## 目录结构（后端示例）
```
src/
├── main/
│   ├── java/com/yourcompany/app/
│   │   ├── controller/
│   │   ├── service/
│   │   ├── repository/
│   │   ├── model/
│   │   ├── config/
│   │   ├── security/
│   │   └── util/
│   └── resources/
│       ├── application.yml
│       └── static/
└── test/
```

## 快速启动
1. 克隆项目到本地
2. 配置数据库（MySQL）和application.yml
3. 构建并启动Spring Boot服务
4. 访问API接口或前端页面

## 文档与支持
- 详细设计文档请见 `详细设计.md`
