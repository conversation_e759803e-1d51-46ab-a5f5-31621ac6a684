# ApplicationCenter Backend 测试脚本

## 简介

本目录包含用于测试ApplicationCenter Backend API的Python脚本。

## 文件说明

- `test_admin_login.py` - 管理员登录测试脚本，用于调试登录问题
- `test_application_api.py` - 应用管理模块API测试脚本
- `test_version_api.py` - 版本管理模块API测试脚本
- `test_log_api.py` - 日志管理模块API测试脚本
- `test_statistics_api.py` - 统计功能模块API测试脚本
- `test_aop_logging.py` - AOP日志记录功能测试脚本
- `requirements.txt` - Python依赖包列表
- `README.md` - 本说明文件

## 环境要求

- Python 3.6+
- requests库

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 管理员登录测试

```bash
python test_admin_login.py
```

这个脚本会测试：
- 健康检查接口
- 管理员登录
- 获取管理员信息
- 多种登录场景测试

### 2. 应用管理测试

```bash
python test_application_api.py
```

这个脚本会测试：
- 应用CRUD操作
- 应用搜索和过滤
- 应用统计功能
- 错误情况处理

### 3. 版本管理测试

```bash
python test_version_api.py
```

这个脚本会测试：
- 版本CRUD操作
- 文件上传功能
- 版本发布和废弃
- 版本统计功能

### 4. 日志管理测试

```bash
python test_log_api.py
```

这个脚本会测试：
- 操作日志查询和过滤
- 错误日志查询和过滤
- 日志统计分析
- 日志清理功能

### 5. 统计功能测试

```bash
python test_statistics_api.py
```

这个脚本会测试：
- 系统概览统计
- 应用和版本统计
- 用户活跃度统计
- 日志统计功能

### 6. AOP日志记录测试

```bash
python test_aop_logging.py
```

这个脚本会测试：
- AOP自动日志记录功能
- 不同日志级别记录
- 异常处理日志记录
- 性能影响测试

## 测试前准备

1. 确保后端服务已启动（端口8080）
2. 确保数据库已初始化
3. 确保管理员账号已创建（admin/admin123）

## 测试结果说明

- ✅ 通过：测试成功
- ❌ 失败：测试失败
- ⚠️ 警告：跳过某些测试

## 测试覆盖范围

### 功能测试
- 管理员认证和授权
- 应用管理CRUD操作
- 版本管理CRUD操作
- 文件上传和下载
- 日志查询和过滤
- 统计功能验证

### 性能测试
- API响应时间测试
- 大量数据处理测试
- AOP性能影响测试

### 安全测试
- 未授权访问测试
- 无效Token测试
- 参数验证测试

### 异常测试
- 网络异常处理
- 数据库异常处理
- 业务逻辑异常处理

## 故障排除

1. **连接被拒绝**：检查后端服务是否启动
2. **登录失败**：检查数据库中的管理员账号
3. **Token无效**：检查JWT配置
4. **JSON解析错误**：检查API响应格式

## 注意事项

- 测试脚本会向数据库写入测试数据
- 建议在测试环境中运行
- 生产环境请谨慎使用 