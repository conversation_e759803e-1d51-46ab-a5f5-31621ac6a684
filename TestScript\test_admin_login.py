#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员登录测试脚本 - 用于调试登录问题
"""

import requests
import json

def test_admin_login(username, password):
    """测试管理员登录"""
    print(f"=== 测试管理员登录: {username} ===")
    try:
        data = {
            "username": username,
            "password": password
        }
        response = requests.post(
            "http://localhost:8080/api/admin/login",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            response_data = response.json()
            if response_data.get("code") == "200":
                print("✅ 登录成功")
                return response_data["data"]["token"]
            else:
                print(f"❌ 登录失败: {response_data.get('message')}")
                return None
        
        print("❌ 登录失败")
        return None
    except Exception as e:
        print(f"错误: {e}")
        return None

def test_health_check():
    """测试健康检查"""
    print("=== 测试健康检查 ===")
    try:
        response = requests.get("http://localhost:8080/api/admin/health")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"错误: {e}")
        return False

def test_get_admin_info(token):
    """测试获取管理员信息"""
    print("\n=== 测试获取管理员信息 ===")
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        response = requests.get(
            "http://localhost:8080/api/admin/users/1",
            headers=headers
        )
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"错误: {e}")
        return False

# 修改main函数，登录成功后调用test_get_admin_info

def main():
    """主函数"""
    print("管理员登录调试测试")
    print("=" * 50)
    
    # 测试健康检查
    health_ok = test_health_check()
    if not health_ok:
        print("❌ 服务不可用，请检查后端服务是否启动")
        return
    
    # 测试不同的用户名和密码组合
    test_cases = [
        ("admin", "admin123"),
        ("test", "test123"),
        ("admin", "wrong_password"),
        ("nonexistent", "admin123"),
        ("", ""),
        ("admin", ""),
        ("", "admin123")
    ]
    
    token = None
    for username, password in test_cases:
        token = test_admin_login(username, password)
        if token:
            print(f"✅ 成功登录: {username}")
            break
        print("-" * 30)
    
    # 登录成功后测试获取管理员信息
    if token:
        admin_info_ok = test_get_admin_info(token)
    else:
        admin_info_ok = False
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
    print(f"管理员登录: {'✅ 通过' if token else '❌ 失败'}")
    print(f"获取管理员信息: {'✅ 通过' if admin_info_ok else '❌ 失败'}")
    
    if health_ok and token and admin_info_ok:
        print("\n🎉 所有基本功能测试通过！")
    else:
        print("\n⚠️  部分功能测试失败，请检查服务状态")

if __name__ == "__main__":
    main() 