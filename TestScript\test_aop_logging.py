#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AOP日志记录功能测试脚本
验证AOP自动日志记录功能：注解验证、自动记录、异常处理、性能测试等
"""

import requests
import json
import time
from datetime import datetime, timedelta

class AOPLoggingTester:
    def __init__(self):
        self.base_url = "http://localhost:8080/api"
        self.token = None
        self.passed_tests = 0
        self.failed_tests = 0
    
    def login(self):
        """管理员登录获取token"""
        print("\n=== 管理员登录 ===")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        try:
            response = requests.post(f"{self.base_url}/admin/login", json=login_data)
            response_data = response.json()
            if response_data.get("code") == "200":
                self.token = response_data.get("data", {}).get("token")
                print(f"✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {response_data.get('message')}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False

    def test_api(self, test_name, method, url, data=None, custom_headers=None, expect_error=False):
        """通用API测试方法"""
        try:
            headers = {
                'Content-Type': 'application/json'
            }
            if self.token:
                headers['Authorization'] = f'Bearer {self.token}'
            if custom_headers:
                headers.update(custom_headers)

            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=data)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, json=data)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=headers, json=data)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers)
            else:
                print(f"❌ 不支持的HTTP方法: {method}")
                self.failed_tests += 1
                return

            try:
                response_data = response.json()
                if response_data.get("code") == "200":
                    if expect_error:
                        print(f"❌ 意外成功: {test_name}")
                        self.failed_tests += 1
                    else:
                        print(f"✅ {test_name}")
                        self.passed_tests += 1
                else:
                    if expect_error:
                        print(f"✅ 预期失败: {test_name}")
                        self.passed_tests += 1
                    else:
                        print(f"❌ {test_name}: {response_data.get('message', '未知错误')}")
                        self.failed_tests += 1
            except json.JSONDecodeError:
                if expect_error:
                    print(f"✅ 预期JSON错误: {test_name}")
                    self.passed_tests += 1
                else:
                    print(f"❌ {test_name}: JSON解析错误")
                    self.failed_tests += 1
        except requests.exceptions.RequestException as e:
            if expect_error:
                print(f"✅ 预期网络错误: {test_name}")
                self.passed_tests += 1
            else:
                print(f"❌ {test_name}: 网络错误 - {str(e)}")
                self.failed_tests += 1
        except Exception as e:
            print(f"❌ 测试异常: {test_name} - {str(e)}")
            self.failed_tests += 1

    def check_log_recorded(self, operation_name, module_name, timeout=5):
        """检查是否记录了日志"""
        print(f"检查日志记录: {operation_name} - {module_name}")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 查询最近的操作日志
                response = requests.get(
                    f"{self.base_url}/logs/operations",
                    headers={'Authorization': f'Bearer {self.token}'},
                    params={"page": 0, "size": 5}
                )
                
                if response.status_code == 200:
                    response_data = response.json()
                    if response_data.get("code") == "200":
                        logs = response_data.get("data", {}).get("content", [])
                        
                        # 检查是否有匹配的日志记录
                        for log in logs:
                            if (log.get("operation") == operation_name and 
                                log.get("module") == module_name):
                                print(f"✅ 找到日志记录: {log.get('description')}")
                                return True
                
                time.sleep(0.5)  # 等待500ms后重试
                
            except Exception as e:
                print(f"检查日志时出错: {str(e)}")
                time.sleep(0.5)
        
        print(f"❌ 未找到日志记录: {operation_name} - {module_name}")
        return False

    def test_admin_controller_aop(self):
        """测试管理员Controller的AOP日志记录"""
        print("\n=== 测试管理员Controller AOP日志记录 ===")
        
        # 测试管理员登录（应该记录日志）
        print("测试管理员登录...")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(f"{self.base_url}/admin/login", json=login_data)
        
        if response.status_code == 200:
            print("✅ 管理员登录成功")
            # 检查是否记录了日志
            if self.check_log_recorded("login", "管理员管理"):
                self.passed_tests += 1
            else:
                self.failed_tests += 1
        else:
            print("❌ 管理员登录失败")
            self.failed_tests += 1
        
        # 测试获取管理员信息（应该记录日志）
        print("测试获取管理员信息...")
        self.test_api(
            "获取管理员信息",
            "GET",
            f"{self.base_url}/admin/users/1"
        )
        
        # 检查是否记录了日志
        if self.check_log_recorded("getAdmin", "管理员管理"):
            self.passed_tests += 1
        else:
            self.failed_tests += 1

    def test_application_controller_aop(self):
        """测试应用Controller的AOP日志记录"""
        print("\n=== 测试应用Controller AOP日志记录 ===")
        
        # 测试获取应用列表（应该记录日志）
        print("测试获取应用列表...")
        self.test_api(
            "获取应用列表",
            "GET",
            f"{self.base_url}/applications",
            {"page": 0, "size": 10}
        )
        
        # 检查是否记录了日志
        if self.check_log_recorded("getApplicationList", "应用管理"):
            self.passed_tests += 1
        else:
            self.failed_tests += 1
        
        # 测试搜索应用（应该记录日志）
        print("测试搜索应用...")
        self.test_api(
            "搜索应用",
            "GET",
            f"{self.base_url}/applications/search",
            {"keyword": "test"}
        )
        
        # 检查是否记录了日志
        if self.check_log_recorded("searchApplications", "应用管理"):
            self.passed_tests += 1
        else:
            self.failed_tests += 1

    def test_version_controller_aop(self):
        """测试版本Controller的AOP日志记录"""
        print("\n=== 测试版本Controller AOP日志记录 ===")
        
        # 测试获取版本统计（应该记录日志）
        print("测试获取版本统计...")
        self.test_api(
            "获取版本统计",
            "GET",
            f"{self.base_url}/versions/statistics"
        )
        
        # 检查是否记录了日志
        if self.check_log_recorded("getVersionStatistics", "版本管理"):
            self.passed_tests += 1
        else:
            self.failed_tests += 1
        
        # 测试获取平台分布（应该记录日志）
        print("测试获取平台分布...")
        self.test_api(
            "获取平台分布",
            "GET",
            f"{self.base_url}/applications/platforms"
        )
        
        # 检查是否记录了日志
        if self.check_log_recorded("getPlatforms", "应用管理"):
            self.passed_tests += 1
        else:
            self.failed_tests += 1

    def test_exception_handling(self):
        """测试异常处理的AOP日志记录"""
        print("\n=== 测试异常处理的AOP日志记录 ===")
        
        # 测试无效请求（应该记录操作日志，即使返回错误响应）
        print("测试无效请求...")
        self.test_api(
            "测试无效请求",
            "GET",
            f"{self.base_url}/admin/users/999999",  # 不存在的用户ID
            expect_error=True
        )
        
        # 检查是否记录了操作日志（即使是错误响应，也应该记录操作日志）
        time.sleep(1)  # 等待日志记录
        try:
            response = requests.get(
                f"{self.base_url}/logs/operations",
                headers={'Authorization': f'Bearer {self.token}'},
                params={"page": 0, "size": 5}
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == "200":
                    logs = response_data.get("data", {}).get("content", [])
                    if logs:
                        print("✅ 找到操作日志记录")
                        self.passed_tests += 1
                    else:
                        print("❌ 未找到操作日志记录")
                        self.failed_tests += 1
        except Exception as e:
            print(f"检查操作日志时出错: {str(e)}")
            self.failed_tests += 1

    def test_performance_impact(self):
        """测试AOP对性能的影响"""
        print("\n=== 测试AOP对性能的影响 ===")
        
        # 测试多次请求的性能
        print("测试多次请求的性能...")
        start_time = time.time()
        
        for i in range(10):
            response = requests.get(
                f"{self.base_url}/admin/users/1",
                headers={'Authorization': f'Bearer {self.token}'}
            )
            if response.status_code != 200:
                print(f"❌ 第{i+1}次请求失败")
                self.failed_tests += 1
                break
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / 10
        
        print(f"10次请求总时间: {total_time:.3f}秒")
        print(f"平均每次请求时间: {avg_time:.3f}秒")
        
        if avg_time < 1.0:  # 平均时间小于1秒认为性能可接受
            print("✅ AOP性能影响可接受")
            self.passed_tests += 1
        else:
            print("❌ AOP性能影响过大")
            self.failed_tests += 1

    def test_log_levels(self):
        """测试不同日志级别的AOP记录"""
        print("\n=== 测试不同日志级别的AOP记录 ===")
        
        # 测试INFO级别的操作
        print("测试INFO级别操作...")
        self.test_api(
            "INFO级别操作",
            "GET",
            f"{self.base_url}/admin/users/1"
        )
        
        # 测试WARN级别的操作（删除操作）
        print("测试WARN级别操作...")
        # 注意：这里只是测试，不会真正删除数据
        self.test_api(
            "WARN级别操作",
            "DELETE",
            f"{self.base_url}/admin/users/999999",  # 不存在的用户
            expect_error=True
        )
        
        # 检查日志级别
        time.sleep(1)
        try:
            response = requests.get(
                f"{self.base_url}/logs/operations",
                headers={'Authorization': f'Bearer {self.token}'},
                params={"page": 0, "size": 10}
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == "200":
                    logs = response_data.get("data", {}).get("content", [])
                    info_logs = [log for log in logs if log.get("logLevel") == "INFO"]
                    warn_logs = [log for log in logs if log.get("logLevel") == "WARN"]
                    
                    print(f"INFO级别日志数量: {len(info_logs)}")
                    print(f"WARN级别日志数量: {len(warn_logs)}")
                    
                    if info_logs and warn_logs:
                        print("✅ 不同日志级别记录正常")
                        self.passed_tests += 1
                    else:
                        print("❌ 日志级别记录异常")
                        self.failed_tests += 1
        except Exception as e:
            print(f"检查日志级别时出错: {str(e)}")
            self.failed_tests += 1

    def test_log_content(self):
        """测试日志内容的完整性"""
        print("\n=== 测试日志内容的完整性 ===")
        
        # 执行一个操作
        self.test_api(
            "测试日志内容完整性",
            "GET",
            f"{self.base_url}/applications",
            {"page": 0, "size": 5}
        )
        
        # 检查日志内容
        time.sleep(1)
        try:
            response = requests.get(
                f"{self.base_url}/logs/operations",
                headers={'Authorization': f'Bearer {self.token}'},
                params={"page": 0, "size": 5}
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == "200":
                    logs = response_data.get("data", {}).get("content", [])
                    
                    if logs:
                        latest_log = logs[0]  # 最新的日志
                        
                        # 检查必要字段
                        required_fields = ["operation", "module", "description", "username", "ipAddress", "createdAt"]
                        missing_fields = []
                        
                        for field in required_fields:
                            if not latest_log.get(field):
                                missing_fields.append(field)
                        
                        if not missing_fields:
                            print("✅ 日志内容完整")
                            self.passed_tests += 1
                        else:
                            print(f"❌ 缺少字段: {missing_fields}")
                            self.failed_tests += 1
                    else:
                        print("❌ 未找到日志记录")
                        self.failed_tests += 1
        except Exception as e:
            print(f"检查日志内容时出错: {str(e)}")
            self.failed_tests += 1

    def run_tests(self):
        """运行所有测试"""
        print("AOP日志记录功能测试")
        print("=" * 50)
        
        # 登录
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        # 运行测试
        self.test_admin_controller_aop()
        self.test_application_controller_aop()
        self.test_version_controller_aop()
        self.test_exception_handling()
        self.test_performance_impact()
        self.test_log_levels()
        self.test_log_content()
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("测试结果汇总")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.failed_tests}")
        print(f"总测试数: {self.passed_tests + self.failed_tests}")
        
        if self.failed_tests == 0:
            print("🎉 所有测试通过！")
        else:
            print(f"⚠️ 有 {self.failed_tests} 个测试失败")

if __name__ == "__main__":
    tester = AOPLoggingTester()
    tester.run_tests() 