#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用管理模块 API 测试脚本
测试应用管理的所有功能：创建、查询、更新、删除、搜索、统计等
"""

import requests
import json
import os

class ApplicationAPITester:
    def __init__(self):
        self.base_url = "http://localhost:8080/api"
        self.token = None
        self.passed_tests = 0
        self.failed_tests = 0
        self.created_application_id = None
    
    def login(self):
        """管理员登录"""
        print("=== 管理员登录 ===")
        try:
            data = {
                "username": "admin",
                "password": "admin123"
            }
            response = requests.post(
                f"{self.base_url}/admin/login",
                json=data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == "200":
                    self.token = response_data["data"]["token"]
                    print("✅ 登录成功，获取到token:", self.token[:50] + "...")
                    return True
                else:
                    print(f"❌ 登录失败: {response_data.get('message')}")
                    return False
            else:
                print(f"❌ 登录失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False
    
    def test_api(self, test_name, method, url, data=None, headers=None, expected_code="200"):
        """通用API测试方法"""
        print(f"\n=== 测试：{test_name} ===")
        
        if headers is None:
            headers = {'Content-Type': 'application/json'}
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        
        print(f"请求URL: {url}")
        if data:
            print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        print(f"请求头: {headers}")
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            elif method.upper() == "POST":
                response = requests.post(url, json=data, headers=headers)
            elif method.upper() == "PUT":
                response = requests.put(url, json=data, headers=headers)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=headers)
            else:
                print(f"❌ 不支持的HTTP方法: {method}")
                self.failed_tests += 1
                return False
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    if response_data.get("code") == expected_code:
                        print(f"✅ {test_name}成功")
                        self.passed_tests += 1
                        return True
                    else:
                        print(f"❌ {test_name}失败: {response_data.get('message')}")
                        self.failed_tests += 1
                        return False
                except Exception as e:
                    print(f"❌ {test_name}异常: {str(e)}")
                    self.failed_tests += 1
                    return False
            else:
                print(f"❌ {test_name}失败，状态码: {response.status_code}")
                self.failed_tests += 1
                return False
        except Exception as e:
            print(f"❌ {test_name}异常: {str(e)}")
            self.failed_tests += 1
            return False
    
    def test_create_application(self):
        """测试创建新应用"""
        print("\n=== 测试：创建新应用 ===")
        data = {
            "name": "测试应用",
            "packageName": "com.test.app",
            "platform": "ANDROID",
            "description": "这是一个测试应用",
            "version": "1.0.0",
            "developer": "测试开发者",
            "category": "工具",
            "iconUrl": "https://example.com/icon.png",
            "downloadUrl": "https://example.com/download",
            "status": 1
        }
        
        headers = {'Content-Type': 'application/json'}
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        
        print(f"请求URL: {self.base_url}/applications")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        print(f"请求头: {headers}")
        
        response = requests.post(f"{self.base_url}/applications", json=data, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        try:
            response_data = response.json()
            if response_data.get("code") == "200":
                self.created_application_id = response_data["data"]["id"]
                print(f"✅ 创建新应用成功，应用ID: {self.created_application_id}")
                self.passed_tests += 1
                return self.created_application_id
            else:
                print(f"❌ 创建新应用失败: {response_data.get('message')}")
                self.failed_tests += 1
                return None
        except Exception as e:
            print(f"❌ 创建新应用异常: {str(e)}")
            self.failed_tests += 1
            return None
    
    def test_get_applications(self):
        """测试获取应用列表"""
        self.test_api(
            test_name="获取应用列表",
            method="GET",
            url=f"{self.base_url}/applications"
        )
    
    def test_get_applications_paged(self):
        """测试获取应用列表（分页）"""
        self.test_api(
            test_name="获取应用列表（分页）",
            method="GET",
            url=f"{self.base_url}/applications?page=0&size=10"
        )
    
    def test_get_application_detail(self, application_id):
        """测试获取应用详情"""
        self.test_api(
            test_name="获取应用详情",
            method="GET",
            url=f"{self.base_url}/applications/{application_id}"
        )
    
    def test_update_application(self, application_id):
        """测试更新应用信息"""
        data = {
            "name": "更新后的测试应用",
            "packageName": "com.test.app.updated",
            "platform": "ANDROID",
            "description": "这是更新后的测试应用描述",
            "version": "1.1.0",
            "developer": "更新后的开发者",
            "category": "工具",
            "iconUrl": "https://example.com/icon_updated.png",
            "downloadUrl": "https://example.com/download_updated",
            "status": 1
        }
        
        self.test_api(
            test_name="更新应用信息",
            method="PUT",
            url=f"{self.base_url}/applications/{application_id}",
            data=data
        )
    
    def test_search_applications(self):
        """测试搜索应用"""
        self.test_api(
            test_name="搜索应用",
            method="GET",
            url=f"{self.base_url}/applications/search?keyword=测试"
        )
    
    def test_get_application_statistics(self):
        """测试获取应用统计信息"""
        self.test_api(
            test_name="获取应用统计信息",
            method="GET",
            url=f"{self.base_url}/applications/statistics"
        )
    
    def test_delete_application(self, application_id):
        """测试删除应用"""
        self.test_api(
            test_name="删除应用",
            method="DELETE",
            url=f"{self.base_url}/applications/{application_id}"
        )
    
    def test_health_check(self):
        """测试健康检查"""
        print("\n=== 测试：健康检查 ===")
        try:
            response = requests.get(f"{self.base_url}/admin/health")
            print(f"请求URL: {self.base_url}/admin/health")
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                print("✅ 健康检查成功")
                return True
            else:
                print("❌ 健康检查失败")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
            return False

    def test_hello_endpoint(self):
        """测试TestController的hello接口"""
        print("\n=== 测试：TestController hello接口 ===")
        try:
            response = requests.get(f"{self.base_url}/test/hello")
            print(f"请求URL: {self.base_url}/test/hello")
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                print("✅ TestController hello接口成功")
                return True
            else:
                print("❌ TestController hello接口失败")
                return False
        except Exception as e:
            print(f"❌ TestController hello接口异常: {str(e)}")
            return False

    def run_tests(self):
        """运行所有测试"""
        print("=== ApplicationCenter 应用管理模块 API 测试 ===")
        
        # 先测试健康检查
        if not self.test_health_check():
            print("❌ 服务不可用，请检查后端服务是否启动")
            return
        
        # 测试TestController接口
        if not self.test_hello_endpoint():
            print("❌ TestController接口不可用，可能存在控制器注册问题")
            return
        
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        print("\n=== 开始应用管理功能测试 ===")
        
        # 先测试获取应用列表（查看现有应用）
        self.test_get_applications()
        self.test_get_applications_paged()
        
        # 创建新应用
        application_id = self.test_create_application()
        if not application_id:
            print("❌ 创建应用失败，后续测试跳过")
            return
        
        # 测试应用详情
        self.test_get_application_detail(application_id)
        
        # 测试更新应用
        self.test_update_application(application_id)
        
        # 测试搜索功能
        self.test_search_applications()
        
        # 测试统计功能
        self.test_get_application_statistics()
        
        # 最后测试删除功能
        self.test_delete_application(application_id)
        
        # 测试总结
        print("\n" + "=" * 60)
        print("测试总结:")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.failed_tests}")
        print(f"总测试数: {self.passed_tests + self.failed_tests}")
        
        if self.failed_tests == 0:
            print("🎉 所有应用管理功能测试通过！")
        else:
            print(f"⚠️  有 {self.failed_tests} 个测试失败，请检查相关功能")

if __name__ == "__main__":
    tester = ApplicationAPITester()
    tester.run_tests() 