#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理模块 API 测试脚本
测试操作日志和错误日志的所有功能：查询、过滤、统计、清理等
"""

import requests
import json
from datetime import datetime, timedelta

class LogAPITester:
    def __init__(self):
        self.base_url = "http://localhost:8080/api"
        self.token = None
        self.passed_tests = 0
        self.failed_tests = 0
    
    def login(self):
        """管理员登录获取token"""
        print("\n=== 管理员登录 ===")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        try:
            response = requests.post(f"{self.base_url}/admin/login", json=login_data)
            response_data = response.json()
            if response_data.get("code") == "200":
                self.token = response_data.get("data", {}).get("token")
                print(f"✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {response_data.get('message')}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False

    def get_valid_error_log_id(self):
        """获取一个有效的错误日志ID用于测试"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.token}'
            }
            response = requests.get(f"{self.base_url}/logs/errors", headers=headers, params={"page": 0, "size": 1})
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == "200":
                    content = response_data.get("data", {}).get("content", [])
                    if content:
                        return content[0].get("id")
            return None
        except Exception as e:
            print(f"⚠️ 获取错误日志ID失败: {str(e)}")
            return None

    def test_api(self, test_name, method, url, data=None, custom_headers=None, expect_error=False):
        """通用API测试方法"""
        try:
            headers = {
                'Content-Type': 'application/json'
            }
            # 只有未传入custom_headers时才自动加token
            if self.token and not custom_headers:
                headers['Authorization'] = f'Bearer {self.token}'
            if custom_headers:
                headers.update(custom_headers)
            
            # 调试信息
            print(f"🔍 测试: {test_name}")
            print(f"   方法: {method}")
            print(f"   URL: {url}")
            if data:
                print(f"   参数: {data}")
            if custom_headers:
                print(f"   自定义头: {custom_headers}")
            print(f"   最终请求头: {headers}")

            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=data)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, json=data)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=headers, json=data)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers)
            else:
                print(f"❌ 不支持的HTTP方法: {method}")
                self.failed_tests += 1
                return

            # 检查HTTP状态码
            if response.status_code == 401 and expect_error:
                print(f"✅ 预期未授权: {test_name}")
                self.passed_tests += 1
                return
            elif response.status_code == 401 and not expect_error:
                print(f"❌ 意外未授权: {test_name}")
                self.failed_tests += 1
                return
            
            try:
                response_data = response.json()
                if response_data.get("code") == "200":
                    if expect_error:
                        print(f"❌ 意外成功: {test_name}")
                        self.failed_tests += 1
                    else:
                        print(f"✅ {test_name}")
                        self.passed_tests += 1
                else:
                    if expect_error:
                        print(f"✅ 预期失败: {test_name}")
                        self.passed_tests += 1
                    else:
                        print(f"❌ {test_name}: {response_data.get('message', '未知错误')}")
                        self.failed_tests += 1
            except json.JSONDecodeError:
                if expect_error:
                    print(f"✅ 预期JSON错误: {test_name}")
                    self.passed_tests += 1
                else:
                    print(f"❌ {test_name}: JSON解析错误")
                    self.failed_tests += 1
        except requests.exceptions.RequestException as e:
            if expect_error:
                print(f"✅ 预期网络错误: {test_name}")
                self.passed_tests += 1
            else:
                print(f"❌ {test_name}: 网络错误 - {str(e)}")
                self.failed_tests += 1
        except Exception as e:
            print(f"❌ 测试异常: {test_name} - {str(e)}")
            self.failed_tests += 1

    def test_operation_logs(self):
        """测试操作日志相关接口"""
        print("\n=== 测试操作日志接口 ===")
        
        # 获取操作日志列表
        self.test_api(
            "获取操作日志列表",
            "GET",
            f"{self.base_url}/logs/operations",
            {"page": 0, "size": 10}
        )
        
        # 根据用户ID获取操作日志
        self.test_api(
            "根据用户ID获取操作日志",
            "GET",
            f"{self.base_url}/logs/operations/user/1",
            {"page": 0, "size": 10}
        )
        
        # 根据模块获取操作日志
        self.test_api(
            "根据模块获取操作日志",
            "GET",
            f"{self.base_url}/logs/operations/module/应用管理",
            {"page": 0, "size": 10}
        )
        
        # 根据时间范围获取操作日志
        end_time = datetime.now()
        start_time = end_time - timedelta(days=7)
        self.test_api(
            "根据时间范围获取操作日志",
            "GET",
            f"{self.base_url}/logs/operations/time-range",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat(),
                "page": 0,
                "size": 10
            }
        )
        
        # 获取最活跃用户
        self.test_api(
            "获取最活跃用户",
            "GET",
            f"{self.base_url}/logs/operations/most-active-users",
            {"page": 0, "size": 10}
        )
        
        # 获取最常用操作
        self.test_api(
            "获取最常用操作",
            "GET",
            f"{self.base_url}/logs/operations/most-common",
            {"page": 0, "size": 10}
        )
        
        # 获取最活跃模块
        self.test_api(
            "获取最活跃模块",
            "GET",
            f"{self.base_url}/logs/operations/most-active-modules",
            {"page": 0, "size": 10}
        )

    def test_error_logs(self):
        """测试错误日志相关接口"""
        print("\n=== 测试错误日志接口 ===")
        
        # 获取错误日志列表
        self.test_api(
            "获取错误日志列表",
            "GET",
            f"{self.base_url}/logs/errors",
            {"page": 0, "size": 10}
        )
        
        # 根据错误类型获取错误日志
        self.test_api(
            "根据错误类型获取错误日志",
            "GET",
            f"{self.base_url}/logs/errors/type/RuntimeException",
            {"page": 0, "size": 10}
        )
        
        # 根据模块获取错误日志
        self.test_api(
            "根据模块获取错误日志",
            "GET",
            f"{self.base_url}/logs/errors/module/应用管理",
            {"page": 0, "size": 10}
        )
        
        # 根据严重程度获取错误日志
        self.test_api(
            "根据严重程度获取错误日志",
            "GET",
            f"{self.base_url}/logs/errors/severity/HIGH",
            {"page": 0, "size": 10}
        )
        
        # 根据时间范围获取错误日志
        end_time = datetime.now()
        start_time = end_time - timedelta(days=7)
        self.test_api(
            "根据时间范围获取错误日志",
            "GET",
            f"{self.base_url}/logs/errors/time-range",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat(),
                "page": 0,
                "size": 10
            }
        )
        
        # 根据解决状态获取错误日志
        self.test_api(
            "根据解决状态获取错误日志",
            "GET",
            f"{self.base_url}/logs/errors/resolved/false",
            {"page": 0, "size": 10}
        )
        
        # 标记错误为已解决 - 使用动态获取的有效ID
        error_log_id = self.get_valid_error_log_id()
        if error_log_id:
            self.test_api(
                "标记错误为已解决",
                "PUT",
                f"{self.base_url}/logs/errors/{error_log_id}/resolve?resolvedByUserId=1"
            )
        else:
            print("⚠️ 跳过标记错误为已解决测试：没有找到有效的错误日志")
            self.passed_tests += 1  # 跳过测试不算失败
        
        # 获取最常见错误类型
        self.test_api(
            "获取最常见错误类型",
            "GET",
            f"{self.base_url}/logs/errors/most-common-types",
            {"page": 0, "size": 10}
        )
        
        # 获取最容易出错的模块
        self.test_api(
            "获取最容易出错的模块",
            "GET",
            f"{self.base_url}/logs/errors/most-error-prone-modules",
            {"page": 0, "size": 10}
        )
        
        # 获取错误严重程度分布
        self.test_api(
            "获取错误严重程度分布",
            "GET",
            f"{self.base_url}/logs/errors/severity-distribution"
        )
        
        # 获取最近的严重错误
        self.test_api(
            "获取最近的严重错误",
            "GET",
            f"{self.base_url}/logs/errors/recent-critical",
            {"page": 0, "size": 10}
        )

    def test_log_cleanup(self):
        """测试日志清理功能"""
        print("\n=== 测试日志清理功能 ===")
        
        # 清理操作日志
        cleanup_time = datetime.now() - timedelta(days=30)
        self.test_api(
            "清理操作日志",
            "DELETE",
            f"{self.base_url}/logs/operations/cleanup?beforeTime={cleanup_time.isoformat()}"
        )
        
        # 清理错误日志
        self.test_api(
            "清理错误日志",
            "DELETE",
            f"{self.base_url}/logs/errors/cleanup?beforeTime={cleanup_time.isoformat()}"
        )
        
        # 清理已解决的错误日志
        self.test_api(
            "清理已解决的错误日志",
            "DELETE",
            f"{self.base_url}/logs/errors/cleanup-resolved"
        )

    def test_error_cases(self):
        """测试错误情况"""
        print("\n=== 测试错误情况 ===")
        
        # 测试无效的页码
        self.test_api(
            "测试无效页码",
            "GET",
            f"{self.base_url}/logs/operations",
            {"page": -1, "size": 10},
            expect_error=True
        )
        
        # 测试无效的页面大小
        self.test_api(
            "测试无效页面大小",
            "GET",
            f"{self.base_url}/logs/operations",
            {"page": 0, "size": 10000},
            expect_error=True
        )
        
        # 测试无效的用户ID
        self.test_api(
            "测试无效用户ID",
            "GET",
            f"{self.base_url}/logs/operations/user/999999",
            {"page": 0, "size": 10}
        )
        
        # 测试无效的模块名称
        self.test_api(
            "测试无效模块名称",
            "GET",
            f"{self.base_url}/logs/operations/module/不存在的模块",
            {"page": 0, "size": 10}
        )
        
        # 测试无效的时间范围
        end_time = datetime.now()
        start_time = end_time + timedelta(days=1)  # 开始时间晚于结束时间
        self.test_api(
            "测试无效时间范围",
            "GET",
            f"{self.base_url}/logs/operations/time-range",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat(),
                "page": 0,
                "size": 10
            },
            expect_error=True
        )

    def test_unauthorized_access(self):
        """测试未授权访问"""
        print("\n=== 测试未授权访问 ===")
        # 临时清空token，防止污染
        old_token = self.token
        self.token = None
        # 不带token访问 - 应该返回401状态码
        headers = {'Content-Type': 'application/json'}
        self.test_api(
            "不带token访问操作日志",
            "GET",
            f"{self.base_url}/logs/operations",
            {"page": 0, "size": 10},
            custom_headers=headers,
            expect_error=True
        )
        # 恢复token
        self.token = old_token
        # 无效token访问 - 应该返回401状态码
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer invalid_token'
        }
        self.test_api(
            "无效token访问操作日志",
            "GET",
            f"{self.base_url}/logs/operations",
            {"page": 0, "size": 10},
            custom_headers=headers,
            expect_error=True
        )

    def run_tests(self):
        """运行所有测试"""
        print("日志管理模块API测试")
        print("=" * 50)
        
        # 登录
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        # 运行测试
        self.test_operation_logs()
        self.test_error_logs()
        self.test_log_cleanup()
        self.test_error_cases()
        self.test_unauthorized_access()
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("测试结果汇总")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.failed_tests}")
        print(f"总测试数: {self.passed_tests + self.failed_tests}")
        
        if self.failed_tests == 0:
            print("🎉 所有测试通过！")
        else:
            print(f"⚠️ 有 {self.failed_tests} 个测试失败")

if __name__ == "__main__":
    tester = LogAPITester()
    tester.run_tests() 