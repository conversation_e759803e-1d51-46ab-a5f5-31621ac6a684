#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计功能模块 API 测试脚本
测试统计功能的所有接口：系统概览、应用统计、版本统计、用户活跃度、日志统计等
"""

import requests
import json
from datetime import datetime, timedelta

class StatisticsAPITester:
    def __init__(self):
        self.base_url = "http://localhost:8080/api"
        self.token = None
        self.passed_tests = 0
        self.failed_tests = 0
    
    def login(self):
        """管理员登录获取token"""
        print("\n=== 管理员登录 ===")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        try:
            response = requests.post(f"{self.base_url}/admin/login", json=login_data)
            response_data = response.json()
            if response_data.get("code") == "200":
                self.token = response_data.get("data", {}).get("token")
                print(f"✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {response_data.get('message')}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False

    def test_api(self, test_name, method, url, data=None, custom_headers=None, expect_error=False):
        """通用API测试方法"""
        try:
            headers = {
                'Content-Type': 'application/json'
            }
            # 只有未传入custom_headers时才自动加token
            if self.token and not custom_headers:
                headers['Authorization'] = f'Bearer {self.token}'
            if custom_headers:
                headers.update(custom_headers)
            
            # 调试信息
            print(f"🔍 测试: {test_name}")
            print(f"   方法: {method}")
            print(f"   URL: {url}")
            if data:
                print(f"   参数: {data}")
            if custom_headers:
                print(f"   自定义头: {custom_headers}")
            print(f"   最终请求头: {headers}")

            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=data)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, json=data)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=headers, json=data)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers)
            else:
                print(f"❌ 不支持的HTTP方法: {method}")
                self.failed_tests += 1
                return

            try:
                response_data = response.json()
                if response_data.get("code") == "200":
                    if expect_error:
                        print(f"❌ 意外成功: {test_name}")
                        self.failed_tests += 1
                    else:
                        print(f"✅ {test_name}")
                        self.passed_tests += 1
                else:
                    if expect_error:
                        print(f"✅ 预期失败: {test_name}")
                        self.passed_tests += 1
                    else:
                        print(f"❌ {test_name}: {response_data.get('message', '未知错误')}")
                        self.failed_tests += 1
            except json.JSONDecodeError:
                if expect_error:
                    print(f"✅ 预期JSON错误: {test_name}")
                    self.passed_tests += 1
                else:
                    print(f"❌ {test_name}: JSON解析错误")
                    self.failed_tests += 1
        except requests.exceptions.RequestException as e:
            if expect_error:
                print(f"✅ 预期网络错误: {test_name}")
                self.passed_tests += 1
            else:
                print(f"❌ {test_name}: 网络错误 - {str(e)}")
                self.failed_tests += 1
        except Exception as e:
            print(f"❌ 测试异常: {test_name} - {str(e)}")
            self.failed_tests += 1

    def test_system_overview(self):
        """测试系统概览统计"""
        print("\n=== 测试系统概览统计 ===")
        
        # 获取系统概览
        self.test_api(
            "获取系统概览",
            "GET",
            f"{self.base_url}/statistics/overview"
        )

    def test_application_statistics(self):
        """测试应用统计"""
        print("\n=== 测试应用统计 ===")
        
        # 获取应用统计信息
        self.test_api(
            "获取应用统计信息",
            "GET",
            f"{self.base_url}/statistics/applications"
        )
        
        # 获取平台分布
        self.test_api(
            "获取平台分布",
            "GET",
            f"{self.base_url}/statistics/platform-distribution"
        )

    def test_version_statistics(self):
        """测试版本统计"""
        print("\n=== 测试版本统计 ===")
        
        # 获取版本统计信息
        self.test_api(
            "获取版本统计信息",
            "GET",
            f"{self.base_url}/statistics/versions"
        )
        
        # 获取版本状态分布
        self.test_api(
            "获取版本状态分布",
            "GET",
            f"{self.base_url}/statistics/version-status-distribution"
        )

    def test_user_activity_statistics(self):
        """测试用户活跃度统计"""
        print("\n=== 测试用户活跃度统计 ===")
        
        # 获取用户活跃度统计
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)
        self.test_api(
            "获取用户活跃度统计",
            "GET",
            f"{self.base_url}/statistics/user-activity",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat()
            }
        )
        
        # 获取每日活跃用户
        self.test_api(
            "获取每日活跃用户",
            "GET",
            f"{self.base_url}/statistics/daily-active-users",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat()
            }
        )

    def test_log_statistics(self):
        """测试日志统计"""
        print("\n=== 测试日志统计 ===")
        
        # 获取操作日志统计
        end_time = datetime.now()
        start_time = end_time - timedelta(days=7)
        self.test_api(
            "获取操作日志统计",
            "GET",
            f"{self.base_url}/statistics/operation-logs",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat()
            }
        )
        
        # 获取错误日志统计
        self.test_api(
            "获取错误日志统计",
            "GET",
            f"{self.base_url}/statistics/error-logs",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat()
            }
        )
        
        # 获取错误严重程度分布
        self.test_api(
            "获取错误严重程度分布",
            "GET",
            f"{self.base_url}/statistics/error-severity-distribution"
        )
        
        # 获取每日操作统计
        self.test_api(
            "获取每日操作统计",
            "GET",
            f"{self.base_url}/statistics/daily-operations",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat()
            }
        )
        
        # 获取每日错误统计
        self.test_api(
            "获取每日错误统计",
            "GET",
            f"{self.base_url}/statistics/daily-errors",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat()
            }
        )

    def test_file_upload_statistics(self):
        """测试文件上传统计"""
        print("\n=== 测试文件上传统计 ===")
        
        # 获取文件上传统计
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)
        self.test_api(
            "获取文件上传统计",
            "GET",
            f"{self.base_url}/statistics/file-uploads",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat()
            }
        )

    def test_comprehensive_report(self):
        """测试综合报告"""
        print("\n=== 测试综合报告 ===")
        
        # 获取综合报告
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)
        self.test_api(
            "获取综合报告",
            "GET",
            f"{self.base_url}/statistics/comprehensive-report",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat()
            }
        )

    def test_error_cases(self):
        """测试错误情况"""
        print("\n=== 测试错误情况 ===")
        
        # 测试无效的时间范围
        end_time = datetime.now()
        start_time = end_time + timedelta(days=1)  # 开始时间晚于结束时间
        self.test_api(
            "测试无效时间范围",
            "GET",
            f"{self.base_url}/statistics/user-activity",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat()
            },
            expect_error=True
        )
        
        # 测试缺少时间参数
        self.test_api(
            "测试缺少时间参数",
            "GET",
            f"{self.base_url}/statistics/user-activity",
            expect_error=True
        )
        
        # 测试无效的时间格式
        self.test_api(
            "测试无效时间格式",
            "GET",
            f"{self.base_url}/statistics/user-activity",
            {
                "startTime": "invalid-date",
                "endTime": "invalid-date"
            },
            expect_error=True
        )

    def test_unauthorized_access(self):
        """测试未授权访问"""
        print("\n=== 测试未授权访问 ===")
        # 临时清空token，防止污染
        old_token = self.token
        self.token = None
        # 不带token访问 - 应该返回401状态码
        headers = {'Content-Type': 'application/json'}
        self.test_api(
            "不带token访问统计接口",
            "GET",
            f"{self.base_url}/statistics/overview",
            custom_headers=headers,
            expect_error=True
        )
        # 恢复token
        self.token = old_token
        # 无效token访问 - 应该返回401状态码
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer invalid_token'
        }
        self.test_api(
            "无效token访问统计接口",
            "GET",
            f"{self.base_url}/statistics/overview",
            custom_headers=headers,
            expect_error=True
        )

    def test_performance(self):
        """测试性能"""
        print("\n=== 测试性能 ===")
        
        # 测试大量数据查询的性能
        end_time = datetime.now()
        start_time = end_time - timedelta(days=365)  # 查询一年的数据
        self.test_api(
            "测试大量数据查询性能",
            "GET",
            f"{self.base_url}/statistics/user-activity",
            {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat()
            }
        )

    def run_tests(self):
        """运行所有测试"""
        print("统计功能模块API测试")
        print("=" * 50)
        
        # 登录
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        # 运行测试
        self.test_system_overview()
        self.test_application_statistics()
        self.test_version_statistics()
        self.test_user_activity_statistics()
        self.test_log_statistics()
        self.test_file_upload_statistics()
        self.test_comprehensive_report()
        self.test_error_cases()
        self.test_unauthorized_access()
        self.test_performance()
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("测试结果汇总")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.failed_tests}")
        print(f"总测试数: {self.passed_tests + self.failed_tests}")
        
        if self.failed_tests == 0:
            print("🎉 所有测试通过！")
        else:
            print(f"⚠️ 有 {self.failed_tests} 个测试失败")

if __name__ == "__main__":
    tester = StatisticsAPITester()
    tester.run_tests() 