#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本管理模块API测试脚本
测试版本管理的所有功能接口
"""

import requests
import json

class VersionAPITester:
    def __init__(self):
        self.base_url = "http://localhost:8080/api"
        self.token = None
        self.passed_tests = 0
        self.failed_tests = 0

    def login(self):
        """管理员登录获取token"""
        print("\n=== 管理员登录 ===")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        try:
            response = requests.post(f"{self.base_url}/admin/login", json=login_data)
            response_data = response.json()
            if response_data.get("code") == "200":
                self.token = response_data.get("data", {}).get("token")
                print(f"✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {response_data.get('message')}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False

    def test_api(self, test_name, method, url, data=None, files=None, custom_headers=None, expect_error=False):
        """测试API接口"""
        try:
            headers = {
                'Content-Type': 'application/json'
            }
            if self.token:
                headers['Authorization'] = f'Bearer {self.token}'
            if custom_headers:
                headers.update(custom_headers)

            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=data)
            elif method.upper() == 'POST':
                if files:
                    headers.pop('Content-Type', None)
                    response = requests.post(url, headers=headers, files=files, data=data)
                else:
                    response = requests.post(url, headers=headers, json=data)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=headers, json=data)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers)
            elif method.upper() == 'PATCH':
                response = requests.patch(url, headers=headers, json=data)
            else:
                print(f"❌ 不支持的HTTP方法: {method}")
                self.failed_tests += 1
                return

            try:
                response_data = response.json()
                if response_data.get("code") == "200":
                    if expect_error:
                        print(f"❌ 意外成功: {test_name}")
                        self.failed_tests += 1
                    else:
                        print(f"✅ {test_name}")
                        self.passed_tests += 1
                else:
                    if expect_error:
                        print(f"✅ 预期失败: {test_name}")
                        self.passed_tests += 1
                    else:
                        print(f"❌ {test_name}: {response_data.get('message', '未知错误')}")
                        self.failed_tests += 1
            except json.JSONDecodeError:
                if expect_error:
                    print(f"✅ 预期网络错误: {test_name}")
                    self.passed_tests += 1
                else:
                    print(f"❌ {test_name}: JSON解析错误")
                    self.failed_tests += 1
        except requests.exceptions.RequestException as e:
            if expect_error:
                print(f"✅ 预期网络错误: {test_name}")
                self.passed_tests += 1
            else:
                print(f"❌ {test_name}: 网络错误")
                self.failed_tests += 1
        except Exception as e:
            print(f"❌ 测试异常: {test_name}")
            print(f"   错误: {str(e)}")
            self.failed_tests += 1

    def test_create_version(self):
        """测试创建新版本，返回新版本ID"""
        print("\n=== 测试：创建新版本 ===")
        application_id = 1  # 请根据实际情况修改
        version_data = {
            "application": {"id": application_id},
            "versionCode": "1.0.0",
            "versionName": "初始版本",
            "description": "这是第一个版本",
            "minRequiredVersion": "",
            "releaseNotes": "首发",
            "isForcedUpdate": False
        }
        headers = {'Content-Type': 'application/json'}
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        
        response = requests.post(f"{self.base_url}/versions", headers=headers, json=version_data)
        
        try:
            response_data = response.json()
            if response_data.get("code") == "200":
                self.passed_tests += 1
                version_id = response_data.get("data", {}).get("id")
                print(f"✅ 创建新版本")
                return version_id
            else:
                print(f"❌ 创建新版本: {response_data.get('message', '未知错误')}")
                self.failed_tests += 1
                return None
        except Exception as e:
            print(f"❌ 创建新版本: 异常")
            self.failed_tests += 1
            return None

    def test_upload_version_file(self, version_id):
        """测试上传版本文件"""
        print("\n=== 测试：上传版本文件 ===")
        # 使用脚本所在目录的test.apk文件
        import os
        script_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(script_dir, "test.apk")
        try:
            with open(file_path, "rb") as f:
                files = {"file": (file_path, f, "application/vnd.android.package-archive")}
                
                # 添加调试信息
                headers = {}
                if self.token:
                    headers['Authorization'] = f'Bearer {self.token}'
                # 简化上传调试信息
                print(f"上传文件: {os.path.basename(file_path)}")
                
                self.test_api(
                    test_name="上传版本文件",
                    method="POST",
                    url=f"{self.base_url}/versions/{version_id}/upload",
                    files=files
                )
        except FileNotFoundError:
            print(f"❌ 未找到测试文件: {file_path}，请准备一个测试apk文件")
            self.failed_tests += 1

    def test_get_version_detail(self, version_id):
        print("\n=== 测试：获取版本详情 ===")
        self.test_api(
            test_name="获取版本详情",
            method="GET",
            url=f"{self.base_url}/versions/{version_id}"
        )

    def test_get_versions_by_application(self, application_id):
        print("\n=== 测试：获取应用所有版本列表 ===")
        self.test_api(
            test_name="获取应用所有版本列表",
            method="GET",
            url=f"{self.base_url}/versions/application/{application_id}"
        )
        print("\n=== 测试：获取应用所有版本列表（分页） ===")
        self.test_api(
            test_name="获取应用所有版本列表（分页）",
            method="GET",
            url=f"{self.base_url}/versions/application/{application_id}/page?page=0&size=5"
        )

    def test_publish_version(self, version_id):
        print("\n=== 测试：发布版本 ===")
        self.test_api(
            test_name="发布版本",
            method="PATCH",
            url=f"{self.base_url}/versions/{version_id}/publish"
        )

    def test_deprecate_version(self, version_id):
        print("\n=== 测试：下架版本 ===")
        self.test_api(
            test_name="下架版本",
            method="PATCH",
            url=f"{self.base_url}/versions/{version_id}/deprecate"
        )

    def test_update_version(self, version_id):
        print("\n=== 测试：更新版本信息 ===")
        update_data = {
            "versionName": "更新后的版本",
            "description": "更新描述",
            "minRequiredVersion": "1.0.0",
            "releaseNotes": "修复bug",
            "isForcedUpdate": True
        }
        self.test_api(
            test_name="更新版本信息",
            method="PUT",
            url=f"{self.base_url}/versions/{version_id}",
            data=update_data
        )

    def test_delete_version(self, version_id):
        print("\n=== 测试：删除版本 ===")
        self.test_api(
            test_name="删除版本",
            method="DELETE",
            url=f"{self.base_url}/versions/{version_id}"
        )

    def test_search_versions(self):
        print("\n=== 测试：搜索版本 ===")
        self.test_api(
            test_name="搜索版本",
            method="GET",
            url=f"{self.base_url}/versions/search?keyword=1.0"
        )
        print("\n=== 测试：搜索版本（分页） ===")
        self.test_api(
            test_name="搜索版本（分页）",
            method="GET",
            url=f"{self.base_url}/versions/search/page?keyword=1.0&page=0&size=5"
        )

    def test_get_version_statistics(self):
        print("\n=== 测试：获取版本统计信息 ===")
        headers = {'Content-Type': 'application/json'}
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        
        response = requests.get(f"{self.base_url}/versions/statistics", headers=headers)
        
        try:
            response_data = response.json()
            if response_data.get("code") == "200":
                print("✅ 获取版本统计信息")
                self.passed_tests += 1
                return True
            else:
                print("❌ 获取版本统计信息失败")
                self.failed_tests += 1
                return False
        except Exception as e:
            print(f"❌ 获取版本统计信息异常: {str(e)}")
            self.failed_tests += 1
            return False

    def test_get_download_url(self, version_id):
        print("\n=== 测试：获取下载链接 ===")
        self.test_api(
            test_name="获取下载链接",
            method="GET",
            url=f"{self.base_url}/versions/{version_id}/download-url"
        )

    def test_check_for_update(self, application_id):
        print("\n=== 测试：检查更新 ===")
        self.test_api(
            test_name="检查更新",
            method="GET",
            url=f"{self.base_url}/versions/check-update?applicationId={application_id}&currentVersion=1.0.0"
        )

    def test_set_forced_update(self, version_id):
        print("\n=== 测试：设置强制更新 ===")
        self.test_api(
            test_name="设置强制更新",
            method="PATCH",
            url=f"{self.base_url}/versions/{version_id}/forced-update",
            data={"isForcedUpdate": True}
        )

    def test_get_versions_by_status(self):
        print("\n=== 测试：根据状态获取版本列表 ===")
        self.test_api(
            test_name="根据状态获取版本列表",
            method="GET",
            url=f"{self.base_url}/versions/status/PUBLISHED"
        )
        print("\n=== 测试：根据状态获取版本列表（分页） ===")
        self.test_api(
            test_name="根据状态获取版本列表（分页）",
            method="GET",
            url=f"{self.base_url}/versions/status/PUBLISHED/page?page=0&size=5"
        )

    def test_get_forced_update_version(self, application_id):
        print("\n=== 测试：获取应用的强制更新版本 ===")
        self.test_api(
            test_name="获取应用的强制更新版本",
            method="GET",
            url=f"{self.base_url}/versions/forced-update/{application_id}"
        )

    def test_get_applications(self):
        print("\n=== 测试：获取应用列表（公开接口） ===")
        headers = {'Content-Type': 'application/json'}
        
        response = requests.get(f"{self.base_url}/applications/public", headers=headers)
        
        try:
            response_data = response.json()
            if response_data.get("code") == "200":
                print("✅ 获取应用列表")
                self.passed_tests += 1
                return True
            else:
                print("❌ 获取应用列表失败")
                self.failed_tests += 1
                return False
        except Exception as e:
            print(f"❌ 获取应用列表异常: {str(e)}")
            self.failed_tests += 1
            return False

    def run_tests(self):
        print("=== ApplicationCenter 版本管理模块 API 测试 ===")
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        print("\n=== 开始版本管理功能测试 ===")
        
        # 先测试一个公开接口
        self.test_get_applications()
        
        # 再测试一个需要认证的接口
        self.test_get_version_statistics()
        
        application_id = 1  # 请根据实际情况修改
        version_id = self.test_create_version()
        if not version_id:
            print("❌ 创建版本失败，后续测试跳过")
            return
        self.test_upload_version_file(version_id)
        # 立即发布新建版本，确保后续检查更新有PUBLISHED版本
        self.test_publish_version(version_id)
        self.test_get_version_detail(version_id)
        self.test_get_versions_by_application(application_id)
        # 在版本被下架前，先进行需要PUBLISHED状态的测试
        self.test_get_download_url(version_id)
        self.test_check_for_update(application_id)
        self.test_set_forced_update(version_id)
        # 现在可以下架版本了
        self.test_deprecate_version(version_id)
        self.test_update_version(version_id)
        self.test_search_versions()
        self.test_get_version_statistics()
        self.test_get_versions_by_status()
        self.test_get_forced_update_version(application_id)
        self.test_delete_version(version_id)
        print("\n=== 测试完成 ===")
        print(f"通过: {self.passed_tests}")
        print(f"失败: {self.failed_tests}")
        total = self.passed_tests + self.failed_tests
        if total > 0:
            print(f"成功率: {self.passed_tests / total * 100:.2f}%")
        if self.failed_tests == 0:
            print("\n🎉 所有测试通过！")
        else:
            print(f"\n⚠️  有 {self.failed_tests} 个测试失败")

if __name__ == "__main__":
    tester = VersionAPITester()
    tester.run_tests() 