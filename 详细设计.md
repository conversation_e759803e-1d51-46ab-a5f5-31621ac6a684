# APP版本管理系统——详细设计

## 目录
1. [数据库详细设计](#1-数据库详细设计)
2. [接口详细设计](#2-接口详细设计)
3. [前端页面详细设计](#3-前端页面详细设计)
4. [后端架构详细设计](#4-后端架构详细设计)
5. [文件存储与分发详细设计](#5-文件存储与分发详细设计)
6. [权限与安全详细设计](#6-权限与安全详细设计)

---

## 1. 数据库详细设计

### 1.1 管理员表（admin_users）

| 字段名       | 类型         | 约束             | 说明           |
|--------------|--------------|------------------|----------------|
| id           | INT          | PK, AUTO_INCREMENT | 主键，自增     |
| username     | VARCHAR(50)  | UNIQUE, NOT NULL | 管理员用户名   |
| password     | VARCHAR(255) | NOT NULL         | 密码（加密存储）|
| status       | TINYINT      | DEFAULT 1        | 状态(1启用/0禁用)|
| last_login   | DATETIME     |                  | 最后登录时间   |
| created_at   | DATETIME     | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at   | DATETIME     | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

---

### 1.2 应用表（applications）

| 字段名       | 类型         | 约束             | 说明           |
|--------------|--------------|------------------|----------------|
| id           | INT          | PK, AUTO_INCREMENT | 主键，自增     |
| name         | VARCHAR(100) | NOT NULL         | 应用名称       |
| package_name | VARCHAR(100) | NOT NULL         | 包名/Bundle ID |
| platform     | ENUM('ios','android','harmony') | NOT NULL | 平台类型 |
| description  | TEXT         |                  | 应用描述       |
| icon         | VARCHAR(255) |                  | 应用图标URL    |
| category     | VARCHAR(50)  |                  | 应用分类       |
| developer    | VARCHAR(100) |                  | 开发者         |
| website      | VARCHAR(255) |                  | 官网地址       |
| status       | TINYINT      | DEFAULT 1        | 状态(1启用/0禁用)|
| created_by   | INT          |                  | 创建人ID（管理员）|
| created_at   | DATETIME     | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at   | DATETIME     | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

---

### 1.3 版本表（versions）

| 字段名         | 类型         | 约束             | 说明           |
|----------------|--------------|------------------|----------------|
| id             | INT          | PK, AUTO_INCREMENT | 主键，自增     |
| app_id         | INT          | FK, NOT NULL     | 应用ID（外键） |
| version_code   | VARCHAR(20)  | NOT NULL         | 版本号         |
| version_name   | VARCHAR(50)  |                  | 版本名称       |
| build_number   | INT          |                  | 构建号         |
| file_size      | BIGINT       |                  | 文件大小（字节）|
| file_url       | VARCHAR(255) | NOT NULL         | 安装包URL      |
| download_count | INT          | DEFAULT 0        | 下载次数       |
| changelog      | TEXT         |                  | 更新日志       |
| min_os_version | VARCHAR(20)  |                  | 最低系统版本   |
| is_force_update| TINYINT      | DEFAULT 0        | 是否强制更新   |
| status         | ENUM('published','unpublished') | DEFAULT 'published' | 状态（已发布/已下架）|
| published_at   | DATETIME     |                  | 发布时间       |
| created_by     | INT          |                  | 创建人ID（管理员）|
| created_at     | DATETIME     | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at     | DATETIME     | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

---

### 1.4 操作日志表（operation_logs）

| 字段名      | 类型         | 约束             | 说明           |
|-------------|--------------|------------------|----------------|
| id          | INT          | PK, AUTO_INCREMENT | 主键，自增     |
| admin_id    | INT          | FK, NOT NULL     | 管理员ID（外键）|
| action      | VARCHAR(50)  | NOT NULL         | 操作类型       |
| target_type | VARCHAR(50)  | NOT NULL         | 目标类型(app/version)|
| target_id   | INT          |                  | 目标ID         |
| description | TEXT         |                  | 操作描述       |
| ip_address  | VARCHAR(45)  |                  | IP地址         |
| user_agent  | TEXT         |                  | 用户代理       |
| created_at  | DATETIME     | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

---

### 1.5 表关系说明

- 一个管理员可以创建多个应用和版本。
- 一个应用可以有多个版本。
- 操作日志记录管理员对应用和版本的所有操作。

---

## 2. 接口详细设计

### 接口返回格式统一说明
- 所有接口返回值均采用如下结构：
  ```json
  {
    "code": "200",
    "data": { /* 实际返回内容 */ }
  }
  ```
- code为状态码（如200表示成功），data为具体业务数据内容。

---

### 2.1 公开API（无需登录）

#### 1. 获取应用列表
- 接口路径：GET /api/apps
- 请求参数：
  | 参数      | 类型   | 必填 | 说明         |
  |-----------|--------|------|--------------|
  | pageNum   | int    | 否   | 页码，默认1   |
  | pageSize  | int    | 否   | 每页数量，默认10 |
  | category  | string | 否   | 应用分类     |
  | platform  | string | 否   | 平台类型     |
- 返回值：
  ```json
  {
    "code": "200",
    "data": {
      "total": 100,
      "list": [
        {
          "id": 1,
          "name": "App名称",
          "icon": "url",
          "category": "工具",
          "platform": "android",
          "latest_version": "1.2.3"
        }
      ]
    }
  }
  ```

#### 2. 获取应用详情
- 接口路径：GET /api/apps/{id}
- 请求参数：无（路径参数id）
- 返回值：
  ```json
  {
    "code": "200",
    "data": {
      "id": 1,
      "name": "App名称",
      "icon": "url",
      "category": "工具",
      "platform": "android",
      "description": "应用简介",
      "developer": "开发者",
      "website": "官网",
      "latest_version": {
        "id": 10,
        "version_code": "1.2.3",
        "changelog": "更新内容",
        "file_url": "下载地址"
      }
    }
  }
  ```

#### 3. 获取应用版本列表
- 接口路径：GET /api/apps/{id}/versions
- 请求参数：
  | 参数   | 类型 | 必填 | 说明     |
  |--------|------|------|----------|
  | pageNum| int  | 否   | 页码     |
  | pageSize| int | 否   | 每页数量 |
- 返回值：
  ```json
  {
    "code": "200",
    "data": {
      "total": 5,
      "list": [
        {
          "id": 10,
          "version_code": "1.2.3",
          "changelog": "更新内容",
          "file_url": "下载地址",
          "published_at": "2024-06-01"
        }
      ]
    }
  }
  ```

#### 4. 下载统计
- 接口路径：POST /api/versions/{id}/download
- 请求参数：无（路径参数id）
- 返回值：
  ```json
  {
    "code": "200",
    "data": {
      "success": true
    }
  }
  ```

#### 5. 获取最新版本及更新内容
- 接口路径：GET /api/apps/{id}/latest
- 请求参数：
  | 参数         | 类型   | 必填 | 说明           |
  |--------------|--------|------|----------------|
  | version_code | string | 是   | 当前版本号      |
- 返回值：
  ```json
  {
    "code": "200",
    "data": {
      "latest_version": {
        "id": 12,
        "version_code": "2.0.0",
        "changelog": "最新版本更新内容",
        "file_url": "下载地址",
        "published_at": "2024-06-10"
      },
      "update_logs": [
        {
          "version_code": "1.5.0",
          "changelog": "1.5.0更新内容"
        },
        {
          "version_code": "2.0.0",
          "changelog": "2.0.0更新内容"
        }
      ]
    }
  }
  ```
- 说明：
  - 提交当前版本号version_code，返回该应用的最新版本信息。
  - update_logs为当前版本号至最新版本号之间的所有更新内容（按版本号升序）。

---

### 2.2 管理API（需管理员登录）

#### 1. 管理员登录
- 接口路径：POST /api/admin/login
- 请求参数：
  | 参数      | 类型   | 必填 | 说明     |
  |-----------|--------|------|----------|
  | username  | string | 是   | 用户名   |
  | password  | string | 是   | 密码     |
- 返回值：
  ```json
  {
    "code": "200",
    "data": {
      "token": "jwt_token",
      "user": {
        "id": 1,
        "username": "admin"
      }
    }
  }
  ```

#### 2. 应用管理
- 获取应用列表：GET /api/admin/apps
- 创建应用：POST /api/admin/apps
  - 参数：name, package_name, platform, category, description, icon, developer, website
- 更新应用：PUT /api/admin/apps/{id}
- 删除应用：DELETE /api/admin/apps/{id}

#### 3. 版本管理
- 获取版本列表：GET /api/admin/versions?app_id=1
- 上传版本：POST /api/admin/versions
  - 参数：app_id, version_code, version_name, build_number, file_url, changelog, min_os_version, is_force_update
- 发布版本：POST /api/admin/versions/{id}/publish
- 下架版本：POST /api/admin/versions/{id}/unpublish
- 删除版本：DELETE /api/admin/versions/{id}

#### 4. 管理员管理
- 获取管理员列表：GET /api/admin/users
- 新增管理员：POST /api/admin/users
- 修改管理员：PUT /api/admin/users/{id}
- 删除管理员：DELETE /api/admin/users/{id}

#### 5. 操作日志
- 获取操作日志：GET /api/admin/logs?admin_id=1&pageNum=1&pageSize=10
  - 返回值：
  ```json
  {
    "code": "200",
    "data": {
      "total": 20,
      "list": [
        {
          "id": 1,
          "admin_id": 1,
          "action": "新增应用",
          "target_type": "app",
          "target_id": 2,
          "description": "创建了新应用",
          "created_at": "2024-06-10 10:00:00"
        }
      ]
    }
  }
  ```

---

## 3. 前端页面详细设计

### 3.1 主页/应用下载中心

#### 页面结构
- 顶部导航栏：Logo、应用下载中心标题、右上角“管理员后台”与“API文档”按钮
- 应用展示区：卡片网格布局，展示所有应用
- 筛选区：平台筛选、分类筛选
- 搜索框：按应用名称模糊搜索

#### 主要UI元素
- 应用卡片：图标、名称、简介、平台、最新版本号、下载按钮
- 分页组件：支持pageNum、pageSize切换
- 筛选下拉框、搜索输入框

#### 交互说明
- 点击应用卡片进入应用详情页
- 下载按钮直接下载最新版本
- 筛选/搜索/分页实时刷新应用列表

---

### 3.2 应用详情页

#### 页面结构
- 顶部导航栏同主页
- 应用基本信息区：图标、名称、平台、分类、简介、开发者、官网
- 版本信息区：最新版本信息、历史版本列表（可展开）
- 下载按钮：下载最新版本
- 更新日志：展示各版本changelog

#### 主要UI元素
- 信息展示区
- 历史版本表格/列表
- 下载按钮

#### 交互说明
- 点击历史版本可下载对应版本
- 展示当前版本与历史版本的更新内容

---

### 3.3 管理员后台

#### 登录页
- 用户名、密码输入框
- 登录按钮
- 登录成功跳转后台首页

#### 后台主界面
- 左侧导航栏：应用管理、版本管理、管理员管理、操作日志、数据统计、系统设置
- 右侧内容区：对应功能的操作界面

##### 应用管理页
- 应用列表表格：支持增删改查
- 新增/编辑弹窗：填写应用信息
- 删除确认弹窗

##### 版本管理页
- 版本列表表格：支持上传、发布、下架、删除
- 上传/编辑弹窗：填写版本信息、上传安装包
- 发布/下架操作按钮

##### 管理员管理页
- 管理员列表表格：支持增删改查

##### 操作日志页
- 日志列表表格：支持筛选、分页

##### 数据统计页
- 应用数量、版本数量、下载量等统计图表

---

### 3.4 API文档页面

#### 页面结构
- 顶部导航栏同主页
- 接口列表区：分组展示所有API
- 接口详情区：请求方式、路径、参数、返回示例
- 在线测试区（可选）

#### 主要UI元素
- 接口分组导航
- 参数表格、返回示例代码块
- 在线测试表单

#### 交互说明
- 点击接口分组或接口名称，右侧展示接口详情
- 可填写参数进行在线测试（如实现）

---

## 4. 后端架构详细设计

### 4.1 技术选型

- 开发语言：Java（Spring Boot）
- 数据库：MySQL
- 文件存储：本地文件系统（后续可扩展OSS、MinIO等）
- 认证机制：JWT Token
- API风格：RESTful

---

### 4.2 目录结构（Spring Boot 典型结构）

```
src/
├── main/
│   ├── java/com/yourcompany/app/
│   │   ├── controller/      # 控制器，处理HTTP请求
│   │   ├── service/         # 业务逻辑
│   │   ├── service/impl/    # 业务实现
│   │   ├── model/           # 实体类（POJO/Entity）
│   │   ├── repository/      # 数据访问层（DAO/Repository）
│   │   ├── config/          # 配置类
│   │   ├── security/        # 安全与认证
│   │   ├── util/            # 工具类
│   │   └── AppApplication.java # 启动类
│   └── resources/
│       ├── application.yml  # 配置文件
│       ├── static/          # 静态资源
│       └── templates/       # 模板（如有）
└── test/                    # 测试代码
```

---

### 4.3 主要模块/类设计（Spring Boot）

- controller：REST API接口层，接收和响应前端请求
- service：业务逻辑层，处理具体业务
- repository：数据访问层，操作MySQL数据库
- model/entity：实体类，对应数据库表
- security：JWT认证、权限校验
- config：全局配置、文件上传配置、跨域配置等
- util：通用工具类（如文件处理、加密、日志等）

---

### 4.4 文件存储设计

- 本地存储：上传文件保存到服务器指定目录（如 /data/app-uploads/）
- 文件访问：通过API返回文件URL，前端可直接下载
- 扩展性：后续可通过抽象接口，支持OSS、MinIO等云存储切换

---

### 4.5 关键业务流程（Spring Boot实现建议）

- 登录认证：Spring Security + JWT，登录成功后返回Token，后续请求需携带Token
- 文件上传：使用Spring的MultipartFile，保存到本地目录，返回文件URL
- 数据库操作：使用Spring Data JPA或MyBatis操作MySQL
- 操作日志：AOP切面或拦截器统一记录管理员操作
- 异常处理：全局异常处理（@ControllerAdvice），返回统一错误码和信息

---

### 4.6 安全与扩展

- 安全：接口鉴权、参数校验、文件类型/大小校验
- 扩展：文件存储接口抽象，便于未来切换存储方案
- 配置：数据库、文件路径、JWT密钥等均通过application.yml集中管理

---

## 5. 文件存储与分发详细设计

### 5.1 存储方案
- 采用本地文件系统存储应用安装包、图标等大文件。
- 文件上传后保存到服务器指定目录（如 /data/app-uploads/）。
- 文件路径可按应用ID/日期/类型分级存储，如：/data/app-uploads/{appId}/versions/{versionId}/。

### 5.2 访问方式
- 文件上传接口返回文件URL，前端可通过该URL直接下载。
- 可通过Spring Boot静态资源映射或自定义Controller实现文件访问。

### 5.3 命名规范
- 文件名建议采用唯一ID+原始文件名或时间戳，防止重名覆盖。
- 示例：{versionId}_20240610.apk

### 5.4 权限控制
- 上传接口需管理员Token鉴权。
- 下载接口可公开（如需权限可扩展）。
- 限制上传文件类型（如.apk/.ipa/.png/.jpg等）和大小。

### 5.5 可扩展性
- 文件存储相关操作通过接口抽象，便于未来切换为OSS、MinIO等云存储。
- 文件路径、大小、类型等参数通过application.yml集中配置。

---

## 6. 权限与安全详细设计

### 6.1 认证机制
- 管理员登录认证：采用Spring Security + JWT Token方案。
  - 管理员登录成功后，后端生成JWT Token返回前端。
  - 前端后续所有管理API请求需在Header中携带Token（如：Authorization: Bearer xxx）。
  - 后端通过JWT拦截器校验Token有效性，失效或非法Token拒绝访问。

### 6.2 接口权限控制
- 公开API：无需登录，所有用户可访问（如应用列表、应用详情、版本下载等）。
- 管理API：需管理员Token鉴权，未登录或Token无效时返回401。
- 接口权限统一由Spring Security配置，便于后续扩展角色权限（如只读/只写/超级管理员等）。

### 6.3 数据校验
- 参数校验：所有接口参数均需校验类型、长度、格式、必填项。
  - 使用Spring Validation（@Valid、@NotNull、@Size等注解）自动校验。
- 文件校验：上传文件需校验类型（如.apk/.ipa/.png/.jpg等）、大小、内容合法性。

### 6.4 安全防护
- 防SQL注入：所有数据库操作使用ORM（JPA/MyBatis）参数化查询，禁止拼接SQL。
- 防XSS攻击：对用户输入内容进行转义，前端展示时注意安全输出。
- 防CSRF攻击：管理API需Token鉴权，前端可配合CSRF Token（如有需要）。
- 防暴力破解：登录接口可加验证码、登录失败次数限制等。
- 文件安全：限制上传文件类型和大小，校验文件内容，防止木马/脚本上传。

### 6.5 日志与审计
- 操作日志：所有管理员操作（如登录、增删改应用/版本、文件上传等）均记录到operation_logs表，便于审计追踪。
- 系统日志：统一日志中间件，记录请求、异常、关键事件，便于排查问题。

### 6.6 错误处理与返回
- 统一异常处理：全局异常捕获（@ControllerAdvice），返回统一错误码和错误信息，避免泄露系统内部信息。
- 接口返回结构：所有接口返回统一格式（code/data），错误时返回明确的错误码和错误信息。

### 6.7 配置安全
- 敏感信息加密：数据库密码、JWT密钥等敏感信息通过application.yml集中管理，生产环境可用环境变量或加密配置。
- 依赖安全：定期升级依赖库，修复已知安全漏洞。

---

> 可根据需要逐步细化每一部分内容。 